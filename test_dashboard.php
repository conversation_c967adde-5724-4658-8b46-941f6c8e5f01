<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Créer une instance de l'application Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Simuler une requête
$request = Request::create('/dashboard', 'GET');
$response = $kernel->handle($request);

echo "=== Test d'accès au Dashboard ===\n";
echo "Status Code: " . $response->getStatusCode() . "\n";

if ($response->getStatusCode() === 302) {
    echo "Redirection vers: " . $response->headers->get('Location') . "\n";
    echo "Cela est normal car l'utilisateur n'est pas connecté.\n";
    echo "Le dashboard nécessite une authentification.\n";
} elseif ($response->getStatusCode() === 200) {
    echo "Accès autorisé au dashboard.\n";
} else {
    echo "Erreur: " . $response->getStatusCode() . "\n";
}

echo "\nPour tester le dashboard :\n";
echo "1. Allez sur http://127.0.0.1:8001/login\n";
echo "2. Connectez-<NAME_EMAIL> / password123\n";
echo "3. Vous serez redirigé vers le dashboard\n";

$kernel->terminate($request, $response);
