const plugin = require('tailwindcss/plugin');

module.exports = plugin(function({ addComponents }) {
  addComponents({
    '.pc-horizontal': {
      '@apply relative': {}
    },
    
    '.pc-horizontal-nav': {
      '@apply flex items-center space-x-6': {}
    },
    
    '.pc-horizontal-item': {
      '@apply relative': {}
    },
    
    '.pc-horizontal-link': {
      '@apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors': {},
      color: 'var(--theme-horizontalsubmenucolor, #5b6b79)',
      
      '&:hover': {
        backgroundColor: 'var(--theme-activebg, #f3f5f7)'
      }
    }
  });
});
