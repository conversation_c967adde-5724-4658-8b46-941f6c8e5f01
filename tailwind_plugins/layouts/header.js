const plugin = require('tailwindcss/plugin');

module.exports = plugin(function({ addComponents, theme }) {
  addComponents({
    '.pc-header': {
      '@apply fixed ltr:right-0 rtl:left-0 ltr:max-lg:left-0 ltr:lg:left-sidebar-width rtl:max-lg:right-0 rtl:lg:right-sidebar-width flex h-header-height z-[1025] backdrop-blur-[7px] transition-all duration-200 ease-in-out': {},
      backgroundColor: 'var(--theme-headerbg, rgba(248,249,250, 0.7))',
      color: 'var(--theme-headercolor, #5b6b79)',
      
      '&[data-pc-theme="dark"]': {
        backgroundColor: 'var(--themedark-headerbg, rgba(19, 25, 32, 0.5))',
        color: 'var(--themedark-headercolor, rgba(255, 255, 255, 0.8))',
      }
    },
    
    '.pc-header-content': {
      '@apply flex items-center justify-between h-full px-6 w-full': {}
    },
    
    '.pc-header-brand': {
      '@apply flex items-center space-x-3': {}
    },
    
    '.pc-header-nav': {
      '@apply hidden md:flex items-center space-x-4': {}
    },
    
    '.pc-header-actions': {
      '@apply flex items-center space-x-2': {}
    }
  });
});
