const plugin = require('tailwindcss/plugin');

module.exports = plugin(function({ addComponents, theme }) {
  addComponents({
    '.pc-sidebar': {
      '@apply fixed w-sidebar-width inset-y-0 z-[1026] max-lg:-left-sidebar-width overflow-hidden ltr:border-r rtl:border-l border-dashed transition-all duration-200 ease-in-out': {},
      backgroundColor: 'var(--theme-bodybg, #f8f9fa)',
      borderColor: 'var(--theme-sidebarbordercolor, #bec8d0)',
      
      '&[data-pc-theme="dark"]': {
        backgroundColor: 'var(--themedark-bodybg, #131920)',
        borderColor: 'var(--themedark-sidebarbordercolor, #242d39)',
      }
    },
    
    '.pc-sidebar.mob-sidebar-active': {
      '@apply max-lg:left-0': {}
    },
    
    '.pc-sidebar .navbar-wrapper': {
      '@apply w-sidebar-width bg-inherit': {}
    },
    
    '.pc-sidebar .navbar-content': {
      '@apply relative py-2.5 px-0': {},
      height: 'calc(100vh - 74px)'
    },
    
    '.pc-navbar': {
      '@apply space-y-1 px-3': {}
    },
    
    '.pc-item': {
      '@apply block': {}
    },
    
    '.pc-link': {
      '@apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors': {},
      color: 'var(--theme-sidebarcolor, #5b6b79)',
      
      '&:hover': {
        backgroundColor: 'var(--theme-activebg, #f3f5f7)',
        color: 'var(--primary, #4680FF)'
      },
      
      '&.active': {
        backgroundColor: 'rgba(70, 128, 255, 0.1)',
        color: 'var(--primary, #4680FF)'
      },
      
      '&[data-pc-theme="dark"]': {
        color: 'var(--themedark-sidebarcolor, rgba(255, 255, 255, 0.5))',
        
        '&:hover': {
          backgroundColor: 'var(--themedark-activebg, #19212a)'
        }
      }
    }
  });
});
