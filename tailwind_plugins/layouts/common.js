const plugin = require('tailwindcss/plugin');

module.exports = plugin(function({ addComponents, theme }) {
  addComponents({
    '.pc-container': {
      '@apply relative max-lg:ml-0 ltr:lg:ml-sidebar-width rtl:lg:mr-sidebar-width top-header-height min-h-[calc(100vh_-_135px)] transition-all duration-200 ease-in-out': {}
    },
    
    '.pc-content': {
      '@apply max-sm:p-[15px] px-10 pt-5': {}
    },
    
    '.pc-footer': {
      '@apply relative z-[995] max-lg:ml-0 ltr:lg:ml-sidebar-width rtl:lg:mr-sidebar-width mt-header-height py-[15px] transition-all duration-200 ease-in-out': {}
    },
    
    // Utilitaires de base
    '.text-muted': {
      color: 'var(--theme-secondarytextcolor, rgba(33, 37, 41, 0.75))',
      
      '&[data-pc-theme="dark"]': {
        color: 'var(--themedark-secondarytextcolor, #748892)'
      }
    },
    
    '.bg-body': {
      backgroundColor: 'var(--theme-bodybg, #f8f9fa)',
      
      '&[data-pc-theme="dark"]': {
        backgroundColor: 'var(--themedark-bodybg, #131920)'
      }
    },
    
    '.text-body': {
      color: 'var(--theme-bodycolor, #131920)',
      
      '&[data-pc-theme="dark"]': {
        color: 'var(--themedark-bodycolor, #bfbfbf)'
      }
    },
    
    '.border-theme': {
      borderColor: 'var(--theme-border, #e7eaee)',
      
      '&[data-pc-theme="dark"]': {
        borderColor: 'var(--themedark-border, #303f50)'
      }
    }
  });
});
