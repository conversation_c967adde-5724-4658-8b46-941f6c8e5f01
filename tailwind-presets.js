// Presets pour tailwindcss-themer (version CommonJS)
const presets = [
  {
    name: 'base',
    selectors: [':root'],
    theme: {
      colors: {
        theme: {
          headings: '#1d2630',
          bodycolor: '#131920',
          bodybg: '#f8f9fa',
          border: '#e7eaee',
          secondarytextcolor: 'rgba(33, 37, 41, 0.75)',
          sidebarcolor: '#5b6b79',
          sidebarbordercolor: '#bec8d0',
          sidebaruserbg: '#f3f5f7',
          headerbg: 'rgba( 248,249,250, 0.7)',
          headercolor: '#5b6b79',
          activebg: '#f3f5f7',
          horizontalsubmenubg: '#fff',
          horizontalsubmenucolor: '#5b6b79',
          cardbg: '#fff',
          inputbg: '#fff',
          inputborder: '#bec8d0'
        }
      }
    }
  },
  {
    name: 'dark',
    selectors: ['[data-pc-theme="dark"]'],
    theme: {
      colors: {
        themedark: {
          headings: 'rgba(255, 255, 255, 0.8)',
          bodycolor: '#bfbfbf',
          bodybg: '#131920',
          border: '#303f50',
          secondarytextcolor: '#748892',
          sidebarcolor: 'rgba(255, 255, 255, 0.5)',
          sidebarbordercolor: '#242d39',
          sidebaruserbg: '#1b232d',
          headerbg: 'rgba( 19, 25, 32, 0.5)',
          headercolor: 'rgba(255, 255, 255, 0.8)',
          activebg: '#19212a',
          horizontalsubmenubg: '#263240',
          horizontalsubmenucolor: '#bfbfbf',
          cardbg: '#1b232d',
          inputbg: '#263240',
          inputborder: '#303f50'
        }
      }
    }
  }
];

module.exports = presets;
