/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./resources/**/*.blade.php",
    "./resources/**/*.js",
    "./resources/**/*.vue",
  ],
  darkMode: ['class', '[data-pc-theme="dark"]'],
  theme: {
    fontFamily: {
      sans: [
        '"Inter var", sans-serif',
        {
          fontFeatureSettings: '"salt"'
        }
      ]
    },
    container: {
      center: true,
      padding: '2rem'
    },
    extend: {
      fontSize: {
        sm: '0.75rem',
        base: '0.875rem',
        'f-h1': '38px',
        'f-h2': '30px',
        'f-h3': '24px',
        'f-h4': '20px',
        'f-h5': '16px',
        'f-h6': '14px'
      },
      spacing: {
        'sidebar-width': '280px',
        'header-height': '74px',
        'topbar-height': '60px',
        'sidebar-collapsed-width': '100px',
        'sidebar-collapsed-active-width': '300px',
        'sidebar-tab-width': '75px',
        'sidebar-tab-navbar-width': '320px'
      },
      colors: {
        theme: {
          headings: '#1d2630',
          bodycolor: '#131920',
          bodybg: '#f8f9fa',
          border: '#e7eaee',
          secondarytextcolor: 'rgba(33, 37, 41, 0.75)',
          sidebarcolor: '#5b6b79',
          sidebarbordercolor: '#bec8d0',
          sidebaruserbg: '#f3f5f7',
          headerbg: 'rgba( 248,249,250, 0.7)',
          headercolor: '#5b6b79',
          activebg: '#f3f5f7',
          horizontalsubmenubg: '#fff',
          horizontalsubmenucolor: '#5b6b79',
          cardbg: '#fff',
          inputbg: '#fff',
          inputborder: '#bec8d0'
        },
        themedark: {
          headings: 'rgba(255, 255, 255, 0.8)',
          bodycolor: '#bfbfbf',
          bodybg: '#131920',
          border: '#303f50',
          secondarytextcolor: '#748892',
          sidebarcolor: 'rgba(255, 255, 255, 0.5)',
          sidebarbordercolor: '#242d39',
          sidebaruserbg: '#1b232d',
          headerbg: 'rgba( 19, 25, 32, 0.5)',
          headercolor: 'rgba(255, 255, 255, 0.8)',
          activebg: '#19212a',
          horizontalsubmenubg: '#263240',
          horizontalsubmenucolor: '#bfbfbf',
          cardbg: '#1b232d',
          inputbg: '#263240',
          inputborder: '#303f50'
        },
        primary: {
          DEFAULT: '#4680FF',
          50: '#EBF3FF',
          100: '#D6E7FF',
          200: '#B3D1FF',
          300: '#80B9FF',
          400: '#4680FF',
          500: '#0D47A1',
          600: '#0A3D91',
          700: '#083481',
          800: '#062B71',
          900: '#042261',
          950: '#031A51'
        },
        secondary: {
          DEFAULT: '#5B6B79',
          50: '#F2F4F5',
          100: '#E1E5E8',
          200: '#BEC6CE',
          300: '#9BA8B4',
          400: '#788A99',
          500: '#5B6B79',
          600: '#495662',
          700: '#38424A',
          800: '#262D33',
          900: '#15191C',
          950: '#0C0E10'
        },
        success: {
          DEFAULT: '#2CA87F',
          50: '#dff2ec',
          100: '#c0e5d9',
          200: '#96d4bf',
          300: '#6bc2a5',
          400: '#4cb592',
          500: '#2ca87f',
          600: '#27a077',
          700: '#21976c',
          800: '#1b8d62',
          900: '#107d4f',
          950: '#0a5736'
        },
        danger: {
          DEFAULT: '#DC2626',
          50: '#fadede',
          100: '#f5bebe',
          200: '#ee9393',
          300: '#e76767',
          400: '#e14747',
          500: '#dc2626',
          600: '#d82222',
          700: '#d31c1c',
          800: '#ce1717',
          900: '#c50d0d',
          950: '#a50909'
        },
        warning: {
          DEFAULT: '#E58A00',
          50: '#fbedd9',
          100: '#f7dcb3',
          200: '#f2c580',
          300: '#edad4d',
          400: '#e99c26',
          500: '#e58a00',
          600: '#e28200',
          700: '#de7700',
          800: '#da6d00',
          900: '#d35a00',
          950: '#b54d00'
        },
        info: {
          DEFAULT: '#3EC9D6',
          50: '#e2f7f9',
          100: '#c5eff3',
          200: '#9fe4eb',
          300: '#78d9e2',
          400: '#5bd1dc',
          500: '#3ec9d6',
          600: '#38c3d1',
          700: '#30bccc',
          800: '#28b5c6',
          900: '#1ba9bc',
          950: '#148999'
        },
        dark: {
          DEFAULT: '#212529',
          50: '#E6E8EB',
          100: '#CED2D7',
          200: '#9EA7B1',
          300: '#6E7C89',
          400: '#485059',
          500: '#212529',
          600: '#1A1D21',
          700: '#131618',
          800: '#0D0E10',
          900: '#060607',
          950: '#020303'
        }
      },
      backgroundImage: {
        'checkbox-bg': `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e")`,
        'radio-bg': `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23ffffff'/%3e%3c/svg%3e")`,
        'select-bg': `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%231d2630' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e")`,
        'select-bg-dark': `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23bfbfbf' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e")`
      },
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
        'medium': '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'large': '0 10px 50px -12px rgba(0, 0, 0, 0.25)',
      }
    }
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    function({ addUtilities }) {
      const newUtilities = {
        '.scrollbar-hide': {
          '-ms-overflow-style': 'none',
          'scrollbar-width': 'none',
          '&::-webkit-scrollbar': {
            display: 'none'
          }
        }
      }
      addUtilities(newUtilities)
    }
  ]
};
