# Structure Able Pro - Documentation Complète

## 📁 Structure des Dossiers Créée

```
resources/
├── views/
│   ├── layouts/
│   │   ├── app.blade.php              # Layout principal Able Pro
│   │   ├── partials/
│   │   │   ├── sidebar.blade.php      # Menu latéral avec navigation
│   │   │   ├── header.blade.php       # Header avec notifications et profil
│   │   │   ├── footer.blade.php       # Footer de l'application
│   │   │   └── scripts.blade.php      # Scripts JavaScript globaux
│   │   └── components/                # Composants réutilisables
│   │       ├── card.blade.php         # Composant carte
│   │       ├── button.blade.php       # Composant bouton
│   │       ├── badge.blade.php        # Composant badge
│   │       └── input.blade.php        # Composant input
│   └── pages/                         # Pages de l'application
│       └── example.blade.php          # Page d'exemple
├── css/
│   └── app.css                        # CSS principal avec styles Able Pro
└── js/
    └── app.js                         # JavaScript avec Alpine.js configuré

public/
├── assets/
│   ├── css/
│   │   └── custom.css                 # Styles personnalisés
│   ├── js/
│   │   └── custom.js                  # Scripts personnalisés
│   ├── images/                        # Images de l'application
│   └── fonts/                         # Polices personnalisées
```

## 🎨 Configuration Tailwind CSS

### Palette de Couleurs Able Pro
- **Primary**: Bleu (#3b82f6)
- **Secondary**: Gris (#64748b)
- **Success**: Vert (#22c55e)
- **Warning**: Orange (#f59e0b)
- **Danger**: Rouge (#ef4444)
- **Info**: Cyan (#0ea5e9)

### Classes Personnalisées
- `.sidebar`, `.main-header`, `.main-content`
- `.card`, `.card-header`, `.card-body`, `.card-footer`
- `.btn-*` pour tous les variants de boutons
- `.badge-*` pour tous les variants de badges
- `.form-input`, `.form-label`, `.form-error`

## 🏗️ Layout Principal (app.blade.php)

### Fonctionnalités
- **Responsive**: Adaptation mobile avec sidebar collapsible
- **Alpine.js**: Intégration complète pour l'interactivité
- **Messages Flash**: Gestion automatique des notifications
- **Breadcrumb**: Navigation contextuelle
- **Header Actions**: Zone pour boutons d'actions
- **Meta Tags**: SEO optimisé

### Slots Disponibles
```blade
<x-slot name="pageTitle">Titre de la page</x-slot>
<x-slot name="pageDescription">Description</x-slot>
<x-slot name="breadcrumb">Navigation</x-slot>
<x-slot name="headerActions">Boutons d'action</x-slot>
```

## 🧩 Composants Blade

### Card Component
```blade
<x-layouts.components.card 
    title="Titre" 
    subtitle="Sous-titre"
    shadow="soft|medium|large"
    :border="true">
    Contenu de la carte
    
    <x-slot name="headerActions">
        <button>Action</button>
    </x-slot>
    
    <x-slot name="footer">
        Pied de carte
    </x-slot>
</x-layouts.components.card>
```

### Button Component
```blade
<x-layouts.components.button 
    variant="primary|secondary|success|warning|danger|outline|ghost|link"
    size="xs|sm|md|lg|xl"
    :icon="'<path>...</path>'"
    iconPosition="left|right"
    :loading="false"
    :disabled="false"
    href="url">
    Texte du bouton
</x-layouts.components.button>
```

### Badge Component
```blade
<x-layouts.components.badge 
    variant="primary|secondary|success|warning|danger|info|gray"
    size="sm|md|lg"
    :dot="false"
    :removable="false">
    Texte du badge
</x-layouts.components.badge>
```

### Input Component
```blade
<x-layouts.components.input 
    name="field_name"
    label="Libellé"
    type="text|email|password|tel"
    placeholder="Placeholder"
    :required="false"
    :disabled="false"
    :icon="'<path>...</path>'"
    iconPosition="left|right"
    help="Texte d'aide"
    error="Message d'erreur"
    size="sm|md|lg" />
```

## 🎯 Partials

### Sidebar (sidebar.blade.php)
- **Navigation principale** avec icônes
- **Sections organisées** (Dashboard, Taxes, Administration)
- **Permissions** intégrées avec `@can`
- **Profil utilisateur** en bas
- **Responsive** avec Alpine.js

### Header (header.blade.php)
- **Menu mobile** pour la sidebar
- **Breadcrumb** responsive
- **Recherche** intégrée
- **Notifications** avec dropdown
- **Menu utilisateur** avec profil et déconnexion

### Footer (footer.blade.php)
- **Copyright** et liens utiles
- **Version** de l'application
- **Responsive** et minimaliste

### Scripts (scripts.blade.php)
- **Configuration globale** de l'application
- **Utilitaires JavaScript** (toast, confirm, format)
- **Gestion des formulaires** AJAX
- **Auto-hide** des messages flash
- **Fonctions d'accessibilité**

## 🚀 JavaScript et Alpine.js

### Store Global
```javascript
Alpine.store('app', {
    sidebarOpen: false,
    loading: false,
    notifications: [],
    toggleSidebar(),
    setLoading(state),
    addNotification(notification),
    removeNotification(id)
});
```

### Composants Alpine.js
- `dropdown`: Gestion des menus déroulants
- `modal`: Gestion des modales
- `tabs`: Système d'onglets
- `accordion`: Accordéons

### Directives Personnalisées
- `x-tooltip`: Tooltips automatiques

### Fonctions Globales
- `showToast(message, type)`: Afficher une notification
- `confirmAction(message, callback)`: Confirmation d'action

## 📱 Responsive Design

### Breakpoints
- **Mobile**: < 768px (sidebar cachée)
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

### Classes Utilitaires
- `.mobile-hidden`: Caché sur mobile
- `.tablet-visible`: Visible sur tablette+
- `.desktop-visible`: Visible sur desktop+

## 🎨 Assets Personnalisés

### CSS (custom.css)
- **Animations**: fadeIn, slideIn, pulse
- **Styles de formulaires**: form-floating
- **Notifications**: transitions
- **Modales**: backdrop et contenu
- **Tooltips**: positionnement
- **Loading**: spinners et skeleton
- **Print**: styles d'impression
- **Accessibilité**: sr-only, focus-visible

### JavaScript (custom.js)
- **AblePro.utils**: Fonctions utilitaires
- **AblePro.forms**: Gestion des formulaires
- **AblePro.modal**: Gestion des modales
- **AblePro.table**: Tri et filtrage des tableaux

## 🔧 Configuration

### Vite (vite.config.js)
- **Hot reload** optimisé
- **Chunks manuels** pour vendor
- **Build** optimisé pour production

### Tailwind (tailwind.config.js)
- **Palette Able Pro** complète
- **Polices**: Inter et Roboto
- **Espacements** personnalisés
- **Ombres** personnalisées
- **Plugin scrollbar-hide**

## 📖 Utilisation

### 1. Créer une nouvelle page
```blade
<x-app-layout>
    <x-slot name="pageTitle">Ma Page</x-slot>
    <x-slot name="pageDescription">Description de ma page</x-slot>
    
    <x-layouts.components.card title="Contenu">
        <!-- Votre contenu ici -->
    </x-layouts.components.card>
</x-app-layout>
```

### 2. Ajouter des styles personnalisés
```blade
@push('styles')
    <link rel="stylesheet" href="{{ asset('assets/css/custom.css') }}">
@endpush
```

### 3. Ajouter des scripts personnalisés
```blade
@push('scripts')
    <script src="{{ asset('assets/js/custom.js') }}"></script>
    <script>
        // Votre code JavaScript
    </script>
@endpush
```

### 4. Utiliser les notifications
```javascript
Utils.showToast('Message de succès', 'success');
Utils.showToast('Message d\'erreur', 'error');
```

## 🚀 Prochaines Étapes

1. **Tester** la structure avec `npm run dev`
2. **Créer** vos pages spécifiques
3. **Personnaliser** les couleurs si nécessaire
4. **Ajouter** vos propres composants
5. **Optimiser** pour la production avec `npm run build`

## 📝 Notes Importantes

- **Permissions**: Utilisez `@can` dans la sidebar pour les accès
- **CSRF**: Tokens automatiquement inclus
- **Responsive**: Testez sur tous les appareils
- **Accessibilité**: Structure WCAG 2.1 AA compliant
- **Performance**: Assets optimisés pour la production

La structure est maintenant prête pour le développement de votre application de gestion des taxes ! 🎉
