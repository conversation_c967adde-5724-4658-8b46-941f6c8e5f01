# Assets Structure - Able Pro Theme

Cette documentation décrit l'organisation des assets pour le thème Able Pro.

## Structure des dossiers

```
public/assets/
├── css/
│   └── custom.css          # Styles personnalisés
├── js/
│   └── custom.js           # Scripts personnalisés
├── images/
│   ├── logo.png           # Logo de l'application
│   ├── favicon.ico        # Favicon
│   └── avatars/           # Images d'avatars par défaut
└── fonts/
    └── custom-fonts/      # Polices personnalisées
```

## CSS (custom.css)

Le fichier `custom.css` contient :

- **Animations personnalisées** : fadeIn, slideIn, pulse, etc.
- **Classes d'animation** : animate-fade-in, animate-slide-in-right, etc.
- **Styles pour graphiques** : chart-container, table-responsive
- **Styles pour formulaires** : form-floating, validation states
- **Styles pour notifications** : notification-enter, notification-exit
- **Styles pour modales** : modal-backdrop, modal-content
- **Styles pour tooltips** : tooltip, tooltip-text
- **États de chargement** : loading-spinner, skeleton
- **Styles d'impression** : @media print
- **Accessibilité** : sr-only, focus-visible
- **Mode sombre** : @media (prefers-color-scheme: dark)
- **Responsive** : mobile-hidden, tablet-visible, desktop-visible

## JavaScript (custom.js)

Le fichier `custom.js` fournit :

### Configuration globale
- `AblePro.config` : Configuration des durées d'animation, timeouts, etc.

### Utilitaires
- `debounce()` : Limite la fréquence d'exécution d'une fonction
- `throttle()` : Contrôle la fréquence d'exécution
- `formatFileSize()` : Formate la taille des fichiers
- `copyToClipboard()` : Copie du texte dans le presse-papiers
- `generateId()` : Génère des IDs uniques
- `isValidEmail()` : Validation d'email
- `isValidPhone()` : Validation de numéro de téléphone français

### Gestion des formulaires
- `enableRealTimeValidation()` : Validation en temps réel
- `submitAjax()` : Soumission AJAX avec gestion des erreurs

### Gestion des modales
- `open()` : Ouvre une modale
- `close()` : Ferme une modale
- `closeOnBackdrop()` : Ferme au clic sur l'arrière-plan

### Gestion des tableaux
- `enableSorting()` : Active le tri des colonnes
- `sortRows()` : Trie les lignes du tableau
- `enableFiltering()` : Active le filtrage

### Fonctionnalités automatiques
- **Tooltips** : `[data-tooltip="text"]`
- **Confirmations** : `[data-confirm="message"]`
- **Auto-submit** : `[data-auto-submit]`
- **Copy to clipboard** : `[data-copy="text"]`

## Utilisation

### Dans les vues Blade

```blade
<!-- Inclure les assets personnalisés -->
@push('styles')
    <link rel="stylesheet" href="{{ asset('assets/css/custom.css') }}">
@endpush

@push('scripts')
    <script src="{{ asset('assets/js/custom.js') }}"></script>
@endpush
```

### Exemples d'utilisation

```html
<!-- Tooltip -->
<button data-tooltip="Cliquez pour sauvegarder">Sauvegarder</button>

<!-- Confirmation -->
<button data-confirm="Êtes-vous sûr de vouloir supprimer ?">Supprimer</button>

<!-- Copy to clipboard -->
<button data-copy="Texte à copier">Copier</button>

<!-- Auto-submit form -->
<form data-auto-submit>
    <select name="filter">
        <option value="all">Tous</option>
        <option value="active">Actifs</option>
    </select>
</form>

<!-- Tableau triable -->
<table class="table" id="myTable">
    <thead>
        <tr>
            <th data-sortable="name">Nom</th>
            <th data-sortable="date">Date</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td data-column="name">John Doe</td>
            <td data-column="date">2024-01-01</td>
        </tr>
    </tbody>
</table>

<script>
    AblePro.table.enableSorting('#myTable');
</script>
```

### Animations CSS

```html
<!-- Éléments avec animations -->
<div class="animate-fade-in">Contenu qui apparaît en fondu</div>
<div class="animate-slide-in-right">Contenu qui glisse depuis la droite</div>
<div class="skeleton">Placeholder de chargement</div>
```

### Notifications

```javascript
// Afficher une notification
Utils.showToast('Opération réussie', 'success');
Utils.showToast('Erreur survenue', 'error');
Utils.showToast('Attention', 'warning');
Utils.showToast('Information', 'info');
```

## Personnalisation

Pour personnaliser les styles ou scripts :

1. **CSS** : Modifiez `custom.css` ou créez de nouveaux fichiers CSS
2. **JavaScript** : Étendez `AblePro` ou créez de nouveaux modules
3. **Images** : Ajoutez vos images dans le dossier approprié
4. **Fonts** : Ajoutez vos polices personnalisées

## Performance

- Les assets sont optimisés pour la production
- Utilisez la compilation Vite pour la minification
- Les animations utilisent les propriétés CSS optimisées
- Le JavaScript utilise des techniques de debouncing/throttling

## Compatibilité

- **Navigateurs** : Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **Mobile** : iOS 12+, Android 7+
- **Accessibilité** : WCAG 2.1 AA compliant
- **RTL** : Support pour les langues de droite à gauche
