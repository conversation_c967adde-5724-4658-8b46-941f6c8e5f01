/* Styles personnalisés pour l'application */

/* Animations personnalisées */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes slideInUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}

@keyframes slideInDown {
    from { transform: translateY(-100%); }
    to { transform: translateY(0); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Classes d'animation */
.animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.3s ease-in-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.3s ease-in-out;
}

.animate-slide-in-up {
    animation: slideInUp 0.3s ease-in-out;
}

.animate-slide-in-down {
    animation: slideInDown 0.3s ease-in-out;
}

.animate-pulse-slow {
    animation: pulse 2s infinite;
}

/* Styles pour les graphiques et tableaux */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Styles pour les formulaires */
.form-floating {
    position: relative;
}

.form-floating > .form-control {
    height: calc(3.5rem + 2px);
    padding: 1rem 0.75rem;
}

.form-floating > label {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    padding: 1rem 0.75rem;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
}

/* Styles pour les notifications */
.notification-enter {
    opacity: 0;
    transform: translateX(100%);
}

.notification-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 300ms, transform 300ms;
}

.notification-exit {
    opacity: 1;
    transform: translateX(0);
}

.notification-exit-active {
    opacity: 0;
    transform: translateX(100%);
    transition: opacity 300ms, transform 300ms;
}

/* Styles pour les modales */
.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    max-height: 90vh;
    overflow-y: auto;
}

/* Styles pour les tooltips */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 120px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Styles pour les états de chargement */
.loading-spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Styles pour l'impression */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-break {
        page-break-after: always;
    }
    
    .print-break-before {
        page-break-before: always;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .sidebar,
    .main-header,
    .footer {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
}

/* Styles pour l'accessibilité */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus-visible:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Styles pour les thèmes sombres */
@media (prefers-color-scheme: dark) {
    .dark-mode {
        --bg-primary: #1a202c;
        --bg-secondary: #2d3748;
        --text-primary: #f7fafc;
        --text-secondary: #e2e8f0;
        --border-color: #4a5568;
    }
}

/* Styles responsifs personnalisés */
@media (max-width: 640px) {
    .mobile-hidden {
        display: none;
    }
    
    .mobile-full-width {
        width: 100% !important;
    }
}

@media (min-width: 768px) {
    .tablet-visible {
        display: block;
    }
}

@media (min-width: 1024px) {
    .desktop-visible {
        display: block;
    }
}
