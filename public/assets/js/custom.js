/**
 * Scripts personnalisés pour l'application Able Pro
 */

// Configuration globale
window.AblePro = {
    config: {
        animationDuration: 300,
        toastDuration: 5000,
        apiTimeout: 30000
    },
    
    // Utilitaires
    utils: {
        // Debounce function
        debounce: function(func, wait, immediate) {
            let timeout;
            return function executedFunction(...args) {
                const later = function() {
                    timeout = null;
                    if (!immediate) func(...args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func(...args);
            };
        },
        
        // Throttle function
        throttle: function(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },
        
        // Format file size
        formatFileSize: function(bytes, decimals = 2) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        },
        
        // Copy to clipboard
        copyToClipboard: function(text) {
            if (navigator.clipboard) {
                return navigator.clipboard.writeText(text);
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                try {
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    return Promise.resolve();
                } catch (err) {
                    document.body.removeChild(textArea);
                    return Promise.reject(err);
                }
            }
        },
        
        // Generate random ID
        generateId: function(prefix = 'id') {
            return prefix + '_' + Math.random().toString(36).substr(2, 9);
        },
        
        // Validate email
        isValidEmail: function(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        },
        
        // Validate phone number (French format)
        isValidPhone: function(phone) {
            const re = /^(?:(?:\+|00)33|0)\s*[1-9](?:[\s.-]*\d{2}){4}$/;
            return re.test(phone);
        }
    },
    
    // Gestion des formulaires
    forms: {
        // Validation en temps réel
        enableRealTimeValidation: function(formSelector) {
            const form = document.querySelector(formSelector);
            if (!form) return;
            
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    this.validateField();
                });
                
                input.addEventListener('input', AblePro.utils.debounce(function() {
                    this.validateField();
                }, 500));
            });
        },
        
        // Soumission AJAX
        submitAjax: function(formSelector, options = {}) {
            const form = document.querySelector(formSelector);
            if (!form) return;
            
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                const submitButton = this.querySelector('[type="submit"]');
                const originalText = submitButton.textContent;
                
                // Désactiver le bouton et afficher le loading
                submitButton.disabled = true;
                submitButton.innerHTML = '<span class="loading-spinner"></span> Envoi...';
                
                fetch(this.action, {
                    method: this.method || 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (options.onSuccess) {
                            options.onSuccess(data);
                        } else {
                            Utils.showToast(data.message || 'Opération réussie', 'success');
                            if (data.redirect) {
                                setTimeout(() => window.location.href = data.redirect, 1000);
                            }
                        }
                    } else {
                        if (options.onError) {
                            options.onError(data);
                        } else {
                            Utils.showToast(data.message || 'Une erreur est survenue', 'error');
                        }
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    Utils.showToast('Une erreur est survenue', 'error');
                })
                .finally(() => {
                    // Réactiver le bouton
                    submitButton.disabled = false;
                    submitButton.textContent = originalText;
                });
            });
        }
    },
    
    // Gestion des modales
    modal: {
        open: function(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                modal.classList.add('flex');
                document.body.style.overflow = 'hidden';
                
                // Focus sur le premier élément focusable
                const focusableElement = modal.querySelector('input, button, select, textarea, [tabindex]:not([tabindex="-1"])');
                if (focusableElement) {
                    focusableElement.focus();
                }
            }
        },
        
        close: function(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                modal.classList.remove('flex');
                document.body.style.overflow = '';
            }
        },
        
        closeOnBackdrop: function(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        AblePro.modal.close(modalId);
                    }
                });
            }
        }
    },
    
    // Gestion des tableaux
    table: {
        // Tri des colonnes
        enableSorting: function(tableSelector) {
            const table = document.querySelector(tableSelector);
            if (!table) return;
            
            const headers = table.querySelectorAll('th[data-sortable]');
            headers.forEach(header => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', function() {
                    const column = this.dataset.sortable;
                    const currentOrder = this.dataset.order || 'asc';
                    const newOrder = currentOrder === 'asc' ? 'desc' : 'asc';
                    
                    // Réinitialiser tous les headers
                    headers.forEach(h => {
                        h.dataset.order = '';
                        h.querySelector('.sort-icon')?.remove();
                    });
                    
                    // Mettre à jour le header actuel
                    this.dataset.order = newOrder;
                    const icon = document.createElement('span');
                    icon.className = 'sort-icon ml-1';
                    icon.innerHTML = newOrder === 'asc' ? '↑' : '↓';
                    this.appendChild(icon);
                    
                    // Trier les lignes
                    AblePro.table.sortRows(table, column, newOrder);
                });
            });
        },
        
        sortRows: function(table, column, order) {
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            rows.sort((a, b) => {
                const aValue = a.querySelector(`[data-column="${column}"]`)?.textContent.trim() || '';
                const bValue = b.querySelector(`[data-column="${column}"]`)?.textContent.trim() || '';
                
                // Essayer de convertir en nombre si possible
                const aNum = parseFloat(aValue);
                const bNum = parseFloat(bValue);
                
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return order === 'asc' ? aNum - bNum : bNum - aNum;
                } else {
                    return order === 'asc' 
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });
            
            // Réinsérer les lignes triées
            rows.forEach(row => tbody.appendChild(row));
        },
        
        // Filtrage
        enableFiltering: function(tableSelector, filterInputSelector) {
            const table = document.querySelector(tableSelector);
            const filterInput = document.querySelector(filterInputSelector);
            
            if (!table || !filterInput) return;
            
            filterInput.addEventListener('input', AblePro.utils.debounce(function() {
                const filterValue = this.value.toLowerCase();
                const rows = table.querySelectorAll('tbody tr');
                
                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    row.style.display = text.includes(filterValue) ? '' : 'none';
                });
            }, 300));
        }
    },
    
    // Initialisation
    init: function() {
        // Initialiser les tooltips
        this.initTooltips();
        
        // Initialiser les confirmations
        this.initConfirmations();
        
        // Initialiser les auto-submit
        this.initAutoSubmit();
        
        // Initialiser les copy-to-clipboard
        this.initCopyToClipboard();
    },
    
    initTooltips: function() {
        const tooltips = document.querySelectorAll('[data-tooltip]');
        tooltips.forEach(element => {
            element.addEventListener('mouseenter', function() {
                const text = this.dataset.tooltip;
                const tooltip = document.createElement('div');
                tooltip.className = 'tooltip-popup absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg';
                tooltip.textContent = text;
                document.body.appendChild(tooltip);
                
                const rect = this.getBoundingClientRect();
                tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
                tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
                
                this._tooltip = tooltip;
            });
            
            element.addEventListener('mouseleave', function() {
                if (this._tooltip) {
                    this._tooltip.remove();
                    this._tooltip = null;
                }
            });
        });
    },
    
    initConfirmations: function() {
        document.addEventListener('click', function(e) {
            const element = e.target.closest('[data-confirm]');
            if (element) {
                const message = element.dataset.confirm;
                if (!confirm(message)) {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }
            }
        });
    },
    
    initAutoSubmit: function() {
        const forms = document.querySelectorAll('[data-auto-submit]');
        forms.forEach(form => {
            form.addEventListener('change', function() {
                this.submit();
            });
        });
    },
    
    initCopyToClipboard: function() {
        document.addEventListener('click', function(e) {
            const element = e.target.closest('[data-copy]');
            if (element) {
                const text = element.dataset.copy;
                AblePro.utils.copyToClipboard(text)
                    .then(() => Utils.showToast('Copié dans le presse-papiers', 'success'))
                    .catch(() => Utils.showToast('Erreur lors de la copie', 'error'));
            }
        });
    }
};

// Initialiser quand le DOM est prêt
document.addEventListener('DOMContentLoaded', function() {
    AblePro.init();
});

// Exporter pour utilisation globale
window.AblePro = AblePro;
