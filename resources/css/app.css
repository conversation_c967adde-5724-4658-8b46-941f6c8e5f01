@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Styles de base */
@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        @apply font-sans antialiased;
        font-family: 'Inter', sans-serif;
        background-color: theme('colors.theme.bodybg');
        color: theme('colors.theme.bodycolor');
    }

    [data-pc-theme="dark"] body {
        background-color: theme('colors.themedark.bodybg');
        color: theme('colors.themedark.bodycolor');
    }

    /* Preloader */
    .loader-bg {
        @apply fixed inset-0 z-[9999] flex items-center justify-center;
        background-color: theme('colors.theme.bodybg');
    }

    .loader-track {
        @apply relative w-16 h-1 bg-gray-200 rounded-full overflow-hidden;
    }

    .loader-fill {
        @apply absolute top-0 left-0 h-full bg-primary rounded-full;
        width: 0%;
        animation: loader 2s ease-in-out infinite;
    }

    @keyframes loader {
        0% { width: 0%; left: 0%; }
        50% { width: 75%; left: 25%; }
        100% { width: 0%; left: 100%; }
    }

    /* Hide preloader after page load */
    .loaded .loader-bg {
        @apply opacity-0 pointer-events-none;
        transition: opacity 0.5s ease-out;
    }
}

/* Composants Able Pro */
@layer components {
    /* Sidebar */
    .pc-sidebar {
        @apply fixed w-sidebar-width inset-y-0 z-[1026] max-lg:-left-sidebar-width overflow-hidden ltr:border-r rtl:border-l border-dashed border-theme-sidebarbordercolor dark:border-themedark-sidebarbordercolor bg-theme-bodybg dark:bg-themedark-bodybg transition-all duration-200 ease-in-out;
    }

    .pc-sidebar .navbar-wrapper {
        @apply w-sidebar-width bg-inherit;
    }

    .pc-sidebar .navbar-content {
        @apply relative py-2.5 px-0;
        height: calc(100vh - theme(spacing.header-height));
    }

    .pc-sidebar.mob-sidebar-active {
        @apply max-lg:left-0;
    }

    /* Header */
    .pc-header {
        @apply fixed ltr:right-0 rtl:left-0 ltr:max-lg:left-0 ltr:lg:left-sidebar-width rtl:max-lg:right-0 rtl:lg:right-sidebar-width flex h-header-height z-[1025] backdrop-blur-[7px] bg-theme-headerbg dark:bg-themedark-headerbg text-theme-headercolor dark:text-themedark-headercolor transition-all duration-200 ease-in-out;
    }

    /* Container principal */
    .pc-container {
        @apply relative max-lg:ml-0 ltr:lg:ml-sidebar-width rtl:lg:mr-sidebar-width top-header-height min-h-[calc(100vh_-_135px)] transition-all duration-200 ease-in-out;
    }

    .pc-content {
        @apply max-sm:p-[15px] px-10 pt-5;
    }

    /* Footer */
    .pc-footer {
        @apply relative z-[995] max-lg:ml-0 ltr:lg:ml-sidebar-width rtl:lg:mr-sidebar-width mt-header-height py-[15px] transition-all duration-200 ease-in-out;
    }

    /* Cards */
    .card {
        @apply bg-theme-cardbg dark:bg-themedark-cardbg border border-theme-border dark:border-themedark-border rounded-lg shadow-sm;
    }

    .card-header {
        @apply px-6 py-4 border-b border-theme-border dark:border-themedark-border;
    }

    .card-body {
        @apply p-6;
    }

    /* Buttons */
    .btn {
        @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
    }

    .btn-primary {
        @apply bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500;
    }

    .btn-secondary {
        @apply bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-500;
    }

    .btn-success {
        @apply bg-success-500 text-white hover:bg-success-600 focus:ring-success-500;
    }

    .btn-danger {
        @apply bg-danger-500 text-white hover:bg-danger-600 focus:ring-danger-500;
    }

    .btn-warning {
        @apply bg-warning-500 text-white hover:bg-warning-600 focus:ring-warning-500;
    }

    .btn-info {
        @apply bg-info-500 text-white hover:bg-info-600 focus:ring-info-500;
    }

    .btn-light {
        @apply bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500;
    }

    .btn-dark {
        @apply bg-gray-800 text-white hover:bg-gray-900 focus:ring-gray-500;
    }

    /* Forms */
    .form-control {
        @apply block w-full px-3 py-2 border border-theme-inputborder dark:border-themedark-inputborder rounded-md bg-theme-inputbg dark:bg-themedark-inputbg text-theme-bodycolor dark:text-themedark-bodycolor placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
    }

    .form-label {
        @apply block text-sm font-medium text-theme-headings dark:text-themedark-headings mb-2;
    }

    /* Navigation */
    .pc-navbar {
        @apply space-y-1 px-3;
    }

    .pc-item {
        @apply block;
    }

    .pc-link {
        @apply flex items-center px-3 py-2 text-sm font-medium rounded-md text-theme-sidebarcolor dark:text-themedark-sidebarcolor hover:bg-theme-activebg dark:hover:bg-themedark-activebg hover:text-primary-500 transition-colors;
    }

    .pc-link.active {
        @apply bg-primary-500/10 text-primary-500;
    }

    /* Badges */
    .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }

    /* Dropdowns */
    .dropdown-menu {
        @apply absolute right-0 mt-2 w-48 bg-theme-cardbg dark:bg-themedark-cardbg border border-theme-border dark:border-themedark-border rounded-md shadow-lg z-50;
    }

    .dropdown-item {
        @apply block px-4 py-2 text-sm text-theme-bodycolor dark:text-themedark-bodycolor hover:bg-theme-activebg dark:hover:bg-themedark-activebg;
    }
}

/* Utilitaires personnalisés */
@layer utilities {
    .text-muted {
        @apply text-theme-secondarytextcolor dark:text-themedark-secondarytextcolor;
    }

    .text-shadow {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    /* Classes pour les plugins Able Pro */
    .bg-choice-close-btn {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 16 16'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e");
    }

    .bg-switch-bg {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
        background-color: #e5e7eb;
    }

    .bg-switch-active-bg {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
        background-color: #4680FF;
    }
}

/* Customizer */
@layer components {
    .pct-customizer {
        @apply fixed top-0 right-0 z-[1030] h-full w-80 bg-white shadow-xl transform translate-x-full transition-transform duration-300;
    }

    .pct-customizer.active {
        @apply translate-x-0;
    }

    .pct-c-btn {
        @apply absolute top-1/2 -left-12 transform -translate-y-1/2;
    }

    .pct-c-btn button {
        @apply w-12 h-12 rounded-l-lg shadow-lg;
    }

    .pct-c-content {
        @apply h-full flex flex-col;
    }

    .pct-header {
        @apply flex items-center justify-between p-4 border-b;
    }

    .pct-body {
        @apply flex-1 p-4 overflow-y-auto;
    }

    .pct-section {
        @apply mb-6;
    }

    .pct-section h6 {
        @apply text-sm font-semibold mb-3 text-gray-700;
    }

    .preset-btn {
        @apply p-3 border border-gray-200 rounded-lg text-center transition-colors;
    }

    .preset-btn.active {
        @apply border-primary bg-primary-50 text-primary;
    }

    .preset-btn:hover {
        @apply border-primary-300;
    }
}
