@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Variables CSS pour Able Pro */
:root {
    --primary-color: #3b82f6;
    --secondary-color: #64748b;
    --success-color: #22c55e;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #0ea5e9;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --sidebar-width: 260px;
    --header-height: 70px;
    --border-radius: 8px;
    --box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

/* Styles de base */
@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        @apply font-sans antialiased bg-gray-50 text-gray-900;
        font-family: 'Inter', sans-serif;
    }

    * {
        @apply border-gray-200;
    }
}

/* Composants personnalisés */
@layer components {
    /* Layout principal */
    .main-layout {
        @apply min-h-screen bg-gray-50;
    }

    /* Sidebar */
    .sidebar {
        @apply fixed left-0 top-0 z-40 h-screen w-64 bg-white shadow-lg border-r border-gray-200;
        width: var(--sidebar-width);
    }

    .sidebar-header {
        @apply flex items-center justify-center h-16 px-6 border-b border-gray-200;
        height: var(--header-height);
    }

    .sidebar-menu {
        @apply flex-1 overflow-y-auto py-4;
    }

    .sidebar-menu-item {
        @apply flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200;
    }

    .sidebar-menu-item.active {
        @apply bg-primary-50 text-primary-600 border-r-2 border-primary-600;
    }

    .sidebar-menu-icon {
        @apply w-5 h-5 mr-3;
    }

    /* Header */
    .main-header {
        @apply fixed top-0 right-0 z-30 bg-white shadow-sm border-b border-gray-200;
        height: var(--header-height);
        left: var(--sidebar-width);
    }

    .header-content {
        @apply flex items-center justify-between h-full px-6;
    }

    /* Main content */
    .main-content {
        @apply transition-all duration-300;
        margin-left: var(--sidebar-width);
        margin-top: var(--header-height);
        min-height: calc(100vh - var(--header-height));
    }

    .content-wrapper {
        @apply p-6;
    }

    /* Cards */
    .card {
        @apply bg-white rounded-lg shadow-soft border border-gray-200;
    }

    .card-header {
        @apply px-6 py-4 border-b border-gray-200;
    }

    .card-body {
        @apply p-6;
    }

    .card-footer {
        @apply px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-lg;
    }

    /* Buttons */
    .btn {
        @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
    }

    .btn-primary {
        @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
    }

    .btn-secondary {
        @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
    }

    .btn-success {
        @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
    }

    .btn-warning {
        @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
    }

    .btn-danger {
        @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
    }

    .btn-outline {
        @apply btn border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
    }

    /* Forms */
    .form-input {
        @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500;
    }

    .form-label {
        @apply block text-sm font-medium text-gray-700 mb-2;
    }

    .form-error {
        @apply text-sm text-danger-600 mt-1;
    }

    /* Badges */
    .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }

    .badge-primary {
        @apply badge bg-primary-100 text-primary-800;
    }

    .badge-success {
        @apply badge bg-success-100 text-success-800;
    }

    .badge-warning {
        @apply badge bg-warning-100 text-warning-800;
    }

    .badge-danger {
        @apply badge bg-danger-100 text-danger-800;
    }

    /* Tables */
    .table {
        @apply w-full divide-y divide-gray-200;
    }

    .table-header {
        @apply bg-gray-50;
    }

    .table-header-cell {
        @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
    }

    .table-body {
        @apply bg-white divide-y divide-gray-200;
    }

    .table-cell {
        @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .sidebar {
            @apply transform -translate-x-full;
        }

        .sidebar.open {
            @apply transform translate-x-0;
        }

        .main-header {
            left: 0;
        }

        .main-content {
            margin-left: 0;
        }
    }
}

/* Utilities personnalisées */
@layer utilities {
    .text-shadow {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .bg-gradient-success {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .bg-gradient-warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .bg-gradient-danger {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    }
}
