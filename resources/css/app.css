@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Variables CSS pour Able Pro */
:root {
    --primary-color: #4680FF;
    --secondary-color: #5B6B79;
    --success-color: #2CA87F;
    --warning-color: #E58A00;
    --danger-color: #DC2626;
    --info-color: #3EC9D6;
    --dark-color: #212529;
    --light-color: #f8f9fa;
    --sidebar-width: 280px;
    --header-height: 74px;
    --topbar-height: 60px;
    --border-radius: 8px;
    --box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);

    /* Couleurs thème Able Pro */
    --theme-headings: #1d2630;
    --theme-bodycolor: #131920;
    --theme-bodybg: #f8f9fa;
    --theme-border: #e7eaee;
    --theme-secondarytextcolor: rgba(33, 37, 41, 0.75);
    --theme-sidebarcolor: #5b6b79;
    --theme-sidebarbordercolor: #bec8d0;
    --theme-sidebaruserbg: #f3f5f7;
    --theme-headerbg: rgba(248,249,250, 0.7);
    --theme-headercolor: #5b6b79;
    --theme-activebg: #f3f5f7;
    --theme-cardbg: #fff;
    --theme-inputbg: #fff;
    --theme-inputborder: #bec8d0;
}

/* Variables pour le mode sombre */
[data-pc-theme="dark"] {
    --theme-headings: rgba(255, 255, 255, 0.8);
    --theme-bodycolor: #bfbfbf;
    --theme-bodybg: #131920;
    --theme-border: #303f50;
    --theme-secondarytextcolor: #748892;
    --theme-sidebarcolor: rgba(255, 255, 255, 0.5);
    --theme-sidebarbordercolor: #242d39;
    --theme-sidebaruserbg: #1b232d;
    --theme-headerbg: rgba(19, 25, 32, 0.5);
    --theme-headercolor: rgba(255, 255, 255, 0.8);
    --theme-activebg: #19212a;
    --theme-cardbg: #1b232d;
    --theme-inputbg: #263240;
    --theme-inputborder: #303f50;
}

/* Styles de base */
@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        @apply font-sans antialiased bg-gray-50 text-gray-900;
        font-family: 'Inter', sans-serif;
    }

    * {
        @apply border-gray-200;
    }
}

/* Composants personnalisés */
@layer components {
    /* Layout principal */
    .main-layout {
        @apply min-h-screen bg-gray-50;
    }

    /* Sidebar */
    .sidebar {
        @apply fixed left-0 top-0 z-40 h-screen shadow-lg border-r;
        width: var(--sidebar-width);
        background-color: var(--theme-cardbg);
        border-color: var(--theme-sidebarbordercolor);
    }

    .sidebar-header {
        @apply flex items-center justify-center px-6 border-b;
        height: var(--header-height);
        border-color: var(--theme-sidebarbordercolor);
    }

    .sidebar-menu {
        @apply flex-1 overflow-y-auto py-4;
    }

    .sidebar-menu-item {
        @apply flex items-center px-6 py-3 transition-colors duration-200;
        color: var(--theme-sidebarcolor);
    }

    .sidebar-menu-item:hover {
        background-color: var(--theme-activebg);
        color: var(--primary-color);
    }

    .sidebar-menu-item.active {
        @apply border-r-2;
        background-color: var(--theme-activebg);
        color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .sidebar-menu-icon {
        @apply w-5 h-5 mr-3;
    }

    /* Header */
    .main-header {
        @apply fixed top-0 right-0 z-30 shadow-sm border-b;
        height: var(--header-height);
        left: var(--sidebar-width);
        background-color: var(--theme-headerbg);
        border-color: var(--theme-border);
        backdrop-filter: blur(10px);
    }

    .header-content {
        @apply flex items-center justify-between h-full px-6;
    }

    /* Main content */
    .main-content {
        @apply transition-all duration-300;
        margin-left: var(--sidebar-width);
        margin-top: var(--header-height);
        min-height: calc(100vh - var(--header-height));
        background-color: var(--theme-bodybg);
    }

    .content-wrapper {
        @apply p-6;
    }

    /* Cards */
    .card {
        @apply rounded-lg shadow-soft border;
        background-color: var(--theme-cardbg);
        border-color: var(--theme-border);
    }

    .card-header {
        @apply px-6 py-4 border-b;
        border-color: var(--theme-border);
    }

    .card-body {
        @apply p-6;
    }

    .card-footer {
        @apply px-6 py-4 border-t rounded-b-lg;
        border-color: var(--theme-border);
        background-color: var(--theme-activebg);
    }

    /* Buttons */
    .btn {
        @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
    }

    .btn-primary {
        @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
    }

    .btn-secondary {
        @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
    }

    .btn-success {
        @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
    }

    .btn-warning {
        @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
    }

    .btn-danger {
        @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
    }

    .btn-outline {
        @apply btn border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
    }

    /* Forms */
    .form-input {
        @apply block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-1 transition-colors duration-200;
        background-color: var(--theme-inputbg);
        border-color: var(--theme-inputborder);
        color: var(--theme-bodycolor);
    }

    .form-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 1px var(--primary-color);
    }

    .form-label {
        @apply block text-sm font-medium mb-2;
        color: var(--theme-headings);
    }

    .form-error {
        @apply text-sm mt-1;
        color: var(--danger-color);
    }

    /* Badges */
    .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }

    .badge-primary {
        @apply badge bg-primary-100 text-primary-800;
    }

    .badge-success {
        @apply badge bg-success-100 text-success-800;
    }

    .badge-warning {
        @apply badge bg-warning-100 text-warning-800;
    }

    .badge-danger {
        @apply badge bg-danger-100 text-danger-800;
    }

    /* Tables */
    .table {
        @apply w-full divide-y divide-gray-200;
    }

    .table-header {
        @apply bg-gray-50;
    }

    .table-header-cell {
        @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
    }

    .table-body {
        @apply bg-white divide-y divide-gray-200;
    }

    .table-cell {
        @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .sidebar {
            @apply transform -translate-x-full;
        }

        .sidebar.open {
            @apply transform translate-x-0;
        }

        .main-header {
            left: 0;
        }

        .main-content {
            margin-left: 0;
        }
    }
}

/* Utilities personnalisées */
@layer utilities {
    .text-shadow {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .bg-gradient-success {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .bg-gradient-warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .bg-gradient-danger {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    }
}
