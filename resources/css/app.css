@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Styles de base */
@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        @apply font-sans antialiased;
        font-family: 'Inter', sans-serif;
        background-color: theme('colors.theme.bodybg');
        color: theme('colors.theme.bodycolor');
    }

    [data-pc-theme="dark"] body {
        background-color: theme('colors.themedark.bodybg');
        color: theme('colors.themedark.bodycolor');
    }
}

/* Composants Able Pro */
@layer components {
    /* Sidebar */
    .pc-sidebar {
        @apply fixed w-sidebar-width inset-y-0 z-[1026] max-lg:-left-sidebar-width overflow-hidden ltr:border-r rtl:border-l border-dashed border-theme-sidebarbordercolor dark:border-themedark-sidebarbordercolor bg-theme-bodybg dark:bg-themedark-bodybg transition-all duration-200 ease-in-out;
    }

    .pc-sidebar .navbar-wrapper {
        @apply w-sidebar-width bg-inherit;
    }

    .pc-sidebar .navbar-content {
        @apply relative py-2.5 px-0;
        height: calc(100vh - theme(spacing.header-height));
    }

    .pc-sidebar.mob-sidebar-active {
        @apply max-lg:left-0;
    }

    /* Header */
    .pc-header {
        @apply fixed ltr:right-0 rtl:left-0 ltr:max-lg:left-0 ltr:lg:left-sidebar-width rtl:max-lg:right-0 rtl:lg:right-sidebar-width flex h-header-height z-[1025] backdrop-blur-[7px] bg-theme-headerbg dark:bg-themedark-headerbg text-theme-headercolor dark:text-themedark-headercolor transition-all duration-200 ease-in-out;
    }

    /* Container principal */
    .pc-container {
        @apply relative max-lg:ml-0 ltr:lg:ml-sidebar-width rtl:lg:mr-sidebar-width top-header-height min-h-[calc(100vh_-_135px)] transition-all duration-200 ease-in-out;
    }

    .pc-content {
        @apply max-sm:p-[15px] px-10 pt-5;
    }

    /* Footer */
    .pc-footer {
        @apply relative z-[995] max-lg:ml-0 ltr:lg:ml-sidebar-width rtl:lg:mr-sidebar-width mt-header-height py-[15px] transition-all duration-200 ease-in-out;
    }

    /* Cards */
    .card {
        @apply bg-theme-cardbg dark:bg-themedark-cardbg border border-theme-border dark:border-themedark-border rounded-lg shadow-sm;
    }

    .card-header {
        @apply px-6 py-4 border-b border-theme-border dark:border-themedark-border;
    }

    .card-body {
        @apply p-6;
    }

    /* Buttons */
    .btn {
        @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
    }

    .btn-primary {
        @apply bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500;
    }

    .btn-secondary {
        @apply bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-500;
    }

    .btn-success {
        @apply bg-success-500 text-white hover:bg-success-600 focus:ring-success-500;
    }

    .btn-danger {
        @apply bg-danger-500 text-white hover:bg-danger-600 focus:ring-danger-500;
    }

    .btn-warning {
        @apply bg-warning-500 text-white hover:bg-warning-600 focus:ring-warning-500;
    }

    .btn-info {
        @apply bg-info-500 text-white hover:bg-info-600 focus:ring-info-500;
    }

    .btn-light {
        @apply bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500;
    }

    .btn-dark {
        @apply bg-gray-800 text-white hover:bg-gray-900 focus:ring-gray-500;
    }

    /* Forms */
    .form-control {
        @apply block w-full px-3 py-2 border border-theme-inputborder dark:border-themedark-inputborder rounded-md bg-theme-inputbg dark:bg-themedark-inputbg text-theme-bodycolor dark:text-themedark-bodycolor placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
    }

    .form-label {
        @apply block text-sm font-medium text-theme-headings dark:text-themedark-headings mb-2;
    }

    /* Navigation */
    .pc-navbar {
        @apply space-y-1 px-3;
    }

    .pc-item {
        @apply block;
    }

    .pc-link {
        @apply flex items-center px-3 py-2 text-sm font-medium rounded-md text-theme-sidebarcolor dark:text-themedark-sidebarcolor hover:bg-theme-activebg dark:hover:bg-themedark-activebg hover:text-primary-500 transition-colors;
    }

    .pc-link.active {
        @apply bg-primary-500/10 text-primary-500;
    }

    /* Badges */
    .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }

    /* Dropdowns */
    .dropdown-menu {
        @apply absolute right-0 mt-2 w-48 bg-theme-cardbg dark:bg-themedark-cardbg border border-theme-border dark:border-themedark-border rounded-md shadow-lg z-50;
    }

    .dropdown-item {
        @apply block px-4 py-2 text-sm text-theme-bodycolor dark:text-themedark-bodycolor hover:bg-theme-activebg dark:hover:bg-themedark-activebg;
    }
}

/* Utilitaires personnalisés */
@layer utilities {
    .text-muted {
        @apply text-theme-secondarytextcolor dark:text-themedark-secondarytextcolor;
    }

    .text-shadow {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }
}
