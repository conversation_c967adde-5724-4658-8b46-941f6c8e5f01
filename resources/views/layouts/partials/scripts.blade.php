<!-- Scripts JavaScript -->
<script>
    // Configuration globale
    window.AppConfig = {
        name: '{{ config('app.name') }}',
        url: '{{ config('app.url') }}',
        locale: '{{ app()->getLocale() }}',
        csrf_token: '{{ csrf_token() }}',
    };

    // Fonctions utilitaires
    window.Utils = {
        // Afficher une notification toast
        showToast: function(message, type = 'success') {
            // Créer l'élément toast
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 z-50 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden transform transition-all duration-300 ease-in-out translate-x-full`;
            
            const bgColor = {
                'success': 'bg-success-50 border-success-200',
                'error': 'bg-danger-50 border-danger-200',
                'warning': 'bg-warning-50 border-warning-200',
                'info': 'bg-info-50 border-info-200'
            }[type] || 'bg-gray-50 border-gray-200';
            
            const iconColor = {
                'success': 'text-success-400',
                'error': 'text-danger-400',
                'warning': 'text-warning-400',
                'info': 'text-info-400'
            }[type] || 'text-gray-400';
            
            const textColor = {
                'success': 'text-success-800',
                'error': 'text-danger-800',
                'warning': 'text-warning-800',
                'info': 'text-info-800'
            }[type] || 'text-gray-800';
            
            const icons = {
                'success': '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />',
                'error': '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />',
                'warning': '<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />',
                'info': '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />'
            }[type] || '';
            
            toast.innerHTML = `
                <div class="p-4 ${bgColor} border">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 ${iconColor}" viewBox="0 0 20 20" fill="currentColor">
                                ${icons}
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium ${textColor}">${message}</p>
                        </div>
                        <div class="ml-auto pl-3">
                            <div class="-mx-1.5 -my-1.5">
                                <button type="button" class="inline-flex rounded-md p-1.5 ${iconColor} hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-50 focus:ring-gray-600" onclick="this.closest('.fixed').remove()">
                                    <span class="sr-only">Fermer</span>
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(toast);
            
            // Animation d'entrée
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
                toast.classList.add('translate-x-0');
            }, 100);
            
            // Auto-suppression après 5 secondes
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 5000);
        },

        // Confirmer une action
        confirm: function(message, callback) {
            if (confirm(message)) {
                callback();
            }
        },

        // Formater un nombre
        formatNumber: function(number, decimals = 2) {
            return new Intl.NumberFormat('fr-FR', {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            }).format(number);
        },

        // Formater une devise
        formatCurrency: function(amount, currency = 'EUR') {
            return new Intl.NumberFormat('fr-FR', {
                style: 'currency',
                currency: currency
            }).format(amount);
        },

        // Formater une date
        formatDate: function(date, options = {}) {
            const defaultOptions = {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            return new Intl.DateTimeFormat('fr-FR', { ...defaultOptions, ...options }).format(new Date(date));
        }
    };

    // Gestion des formulaires AJAX
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit des formulaires avec data-auto-submit
        document.querySelectorAll('form[data-auto-submit]').forEach(form => {
            form.addEventListener('change', function() {
                this.submit();
            });
        });

        // Confirmation des actions destructives
        document.querySelectorAll('[data-confirm]').forEach(element => {
            element.addEventListener('click', function(e) {
                const message = this.getAttribute('data-confirm');
                if (!confirm(message)) {
                    e.preventDefault();
                    return false;
                }
            });
        });

        // Auto-hide des messages flash après 5 secondes
        document.querySelectorAll('.alert-auto-hide').forEach(alert => {
            setTimeout(() => {
                alert.style.transition = 'opacity 0.5s ease-out';
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 500);
            }, 5000);
        });
    });

    // Gestion des erreurs globales
    window.addEventListener('error', function(e) {
        console.error('Erreur JavaScript:', e.error);
        // En production, vous pourriez envoyer l'erreur à un service de monitoring
    });

    // Fonction pour recharger la page avec un délai
    window.reloadPage = function(delay = 1000) {
        setTimeout(() => {
            window.location.reload();
        }, delay);
    };

    // Fonction pour rediriger
    window.redirectTo = function(url, delay = 0) {
        setTimeout(() => {
            window.location.href = url;
        }, delay);
    };
</script>

<!-- Scripts spécifiques à Alpine.js -->
<script>
    document.addEventListener('alpine:init', () => {
        // Store global pour l'état de l'application
        Alpine.store('app', {
            sidebarOpen: false,
            loading: false,
            
            toggleSidebar() {
                this.sidebarOpen = !this.sidebarOpen;
            },
            
            setLoading(state) {
                this.loading = state;
            }
        });
    });
</script>
