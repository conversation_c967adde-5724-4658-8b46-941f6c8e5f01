<!-- Sidebar Menu -->
<nav class="pc-sidebar">
    <div class="navbar-wrapper">

        <!-- Logo Section -->
        <div class="m-header flex items-center py-4 px-6 h-header-height">
            <a href="{{ route('dashboard') }}" class="b-brand flex items-center gap-3">
                <img src="{{ asset('assets/images/logo-dark.svg') }}" class="img-fluid logo-lg" alt="{{ config('app.name') }}" onerror="this.src='{{ asset('assets/images/logo.png') }}'">
                <span class="badge bg-success-500/10 text-success-500 rounded-full theme-version">v1.0</span>
            </a>
        </div>

        <!-- Sidebar Content -->
        <div class="navbar-content h-[calc(100vh_-_74px)] py-2.5">

            <!-- User Card -->
            <div class="card pc-user-card mx-[15px] mb-[15px] bg-theme-sidebaruserbg dark:bg-themedark-sidebaruserbg">
                <div class="card-body !p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <img src="{{ Auth::user()->avatar ?? asset('assets/images/user/avatar-1.jpg') }}"
                                 alt="user-image"
                                 class="user-avtar w-[45px] h-[45px] rounded-full"
                                 onerror="this.src='https://ui-avatars.com/api/?name={{ urlencode(Auth::user()->name ?? 'User') }}&color=4680FF&background=EBF3FF'">
                        </div>
                        <div class="flex-grow-1 ml-3">
                            <div class="dropdown">
                                <a href="#" class="dropdown-toggle" data-pc-toggle="dropdown" aria-expanded="false">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1 text-theme-headings dark:text-themedark-headings">
                                                {{ Auth::user()->name ?? 'John Doe' }}
                                            </h6>
                                            <span class="text-xs text-muted">
                                                {{ Auth::user()->email ?? '<EMAIL>' }}
                                            </span>
                                        </div>
                                    </div>
                                </a>
                                <div class="dropdown-menu">
                                    @auth
                                        <a href="{{ route('profile.edit') }}" class="dropdown-item">
                                            <i class="text-lg leading-none ti ti-settings"></i>
                                            <span>Paramètres</span>
                                        </a>
                                        <a href="#" class="dropdown-item">
                                            <i class="text-lg leading-none ti ti-lock"></i>
                                            <span>Verrouiller l'écran</span>
                                        </a>
                                        <form method="POST" action="{{ route('logout') }}" class="inline">
                                            @csrf
                                            <button type="submit" class="dropdown-item w-full text-left">
                                                <i class="text-lg leading-none ti ti-power"></i>
                                                <span>Déconnexion</span>
                                            </button>
                                        </form>
                                    @else
                                        <a href="{{ route('login') }}" class="dropdown-item">
                                            <i class="text-lg leading-none ti ti-login"></i>
                                            <span>Connexion</span>
                                        </a>
                                    @endauth
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Menu -->
            <ul class="pc-navbar">
                <!-- Dashboard -->
                <li class="pc-item pc-caption">
                    <label>Navigation</label>
                    <i class="ti ti-dashboard"></i>
                </li>
                <li class="pc-item">
                    <a href="{{ route('dashboard') }}" class="pc-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                        <span class="pc-micon">
                            <i class="ti ti-dashboard"></i>
                        </span>
                        <span class="pc-mtext">Tableau de bord</span>
                    </a>
                </li>

                <!-- Users Management -->
                @can('view-users')
                <li class="pc-item pc-hasmenu">
                    <a href="#!" class="pc-link">
                        <span class="pc-micon">
                            <i class="ti ti-users"></i>
                        </span>
                        <span class="pc-mtext">Utilisateurs</span>
                        <span class="pc-arrow">
                            <i class="ti ti-chevron-right"></i>
                        </span>
                    </a>
                    <ul class="pc-submenu">
                        <li class="pc-item">
                            <a class="pc-link {{ request()->routeIs('users.index') ? 'active' : '' }}"
                               href="{{ route('users.index') }}">Liste des utilisateurs</a>
                        </li>
                        @can('create-users')
                        <li class="pc-item">
                            <a class="pc-link {{ request()->routeIs('users.create') ? 'active' : '' }}"
                               href="{{ route('users.create') }}">Ajouter un utilisateur</a>
                        </li>
                        @endcan
                    </ul>
                </li>
                @endcan

                <!-- Taxes Management -->
                <li class="pc-item pc-caption">
                    <label>Gestion des Taxes</label>
                    <i class="ti ti-calculator"></i>
                </li>
                <li class="pc-item pc-hasmenu">
                    <a href="#!" class="pc-link">
                        <span class="pc-micon">
                            <i class="ti ti-file-text"></i>
                        </span>
                        <span class="pc-mtext">Déclarations</span>
                        <span class="pc-arrow">
                            <i class="ti ti-chevron-right"></i>
                        </span>
                    </a>
                    <ul class="pc-submenu">
                        <li class="pc-item">
                            <a class="pc-link" href="#">Toutes les déclarations</a>
                        </li>
                        <li class="pc-item">
                            <a class="pc-link" href="#">Nouvelle déclaration</a>
                        </li>
                        <li class="pc-item">
                            <a class="pc-link" href="#">Brouillons</a>
                        </li>
                    </ul>
                </li>

                <li class="pc-item">
                    <a href="#" class="pc-link">
                        <span class="pc-micon">
                            <i class="ti ti-credit-card"></i>
                        </span>
                        <span class="pc-mtext">Paiements</span>
                    </a>
                </li>

                <li class="pc-item">
                    <a href="#" class="pc-link">
                        <span class="pc-micon">
                            <i class="ti ti-chart-bar"></i>
                        </span>
                        <span class="pc-mtext">Rapports</span>
                    </a>
                </li>

                <!-- Administration -->
                @can('admin-access')
                <li class="pc-item pc-caption">
                    <label>Administration</label>
                    <i class="ti ti-settings"></i>
                </li>
                <li class="pc-item">
                    <a href="#" class="pc-link">
                        <span class="pc-micon">
                            <i class="ti ti-settings"></i>
                        </span>
                        <span class="pc-mtext">Paramètres</span>
                    </a>
                </li>
                <li class="pc-item">
                    <a href="#" class="pc-link">
                        <span class="pc-micon">
                            <i class="ti ti-shield-check"></i>
                        </span>
                        <span class="pc-mtext">Permissions</span>
                    </a>
                </li>
                @endcan

                <!-- Components (Example) -->
                <li class="pc-item pc-caption">
                    <label>Composants UI</label>
                    <i class="ti ti-layout-2"></i>
                </li>
                <li class="pc-item pc-hasmenu">
                    <a href="#!" class="pc-link">
                        <span class="pc-micon">
                            <i class="ti ti-layout-2"></i>
                        </span>
                        <span class="pc-mtext">Éléments</span>
                        <span class="pc-arrow">
                            <i class="ti ti-chevron-right"></i>
                        </span>
                    </a>
                    <ul class="pc-submenu">
                        <li class="pc-item">
                            <a class="pc-link" href="#">Alertes</a>
                        </li>
                        <li class="pc-item">
                            <a class="pc-link" href="#">Boutons</a>
                        </li>
                        <li class="pc-item">
                            <a class="pc-link" href="#">Cartes</a>
                        </li>
                        <li class="pc-item">
                            <a class="pc-link" href="#">Formulaires</a>
                        </li>
                        <li class="pc-item">
                            <a class="pc-link" href="#">Tableaux</a>
                        </li>
                        <li class="pc-item">
                            <a class="pc-link {{ request()->routeIs('example') ? 'active' : '' }}" href="{{ route('example') }}">Page d'exemple</a>
                        </li>
                    </ul>
                </li>

            </ul>
        </div>
    </div>
</nav>

<style>
/* Styles pour le menu déroulant */
.pc-hasmenu .pc-submenu {
    display: none;
    padding-left: 1rem;
}

.pc-hasmenu.pc-trigger .pc-submenu {
    display: block;
}

.pc-hasmenu .pc-arrow {
    transition: transform 0.2s ease;
}

.pc-hasmenu.pc-trigger .pc-arrow {
    transform: rotate(90deg);
}

/* Amélioration de la carte utilisateur */
.pc-user-card {
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pc-user-card .dropdown-menu {
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
}

.pc-user-card .dropdown-item {
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pc-user-card .dropdown-item:hover {
    background-color: var(--theme-activebg);
}

/* Responsive sidebar */
@media (max-width: 991.98px) {
    .pc-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .pc-sidebar.mob-sidebar-active {
        transform: translateX(0);
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle submenu toggle
    const menuItems = document.querySelectorAll('.pc-hasmenu > .pc-link');

    menuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const parent = this.parentElement;
            const isOpen = parent.classList.contains('pc-trigger');

            // Close all other submenus
            document.querySelectorAll('.pc-hasmenu.pc-trigger').forEach(menu => {
                if (menu !== parent) {
                    menu.classList.remove('pc-trigger');
                }
            });

            // Toggle current submenu
            parent.classList.toggle('pc-trigger', !isOpen);
        });
    });

    // Auto-open submenu if current page is in submenu
    const activeSubmenuLink = document.querySelector('.pc-submenu .pc-link.active');
    if (activeSubmenuLink) {
        const parentMenu = activeSubmenuLink.closest('.pc-hasmenu');
        if (parentMenu) {
            parentMenu.classList.add('pc-trigger');
        }
    }
});
</script>
