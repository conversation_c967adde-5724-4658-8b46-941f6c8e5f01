<!-- Sidebar -->
<aside class="pc-sidebar"
       :class="{ 'mob-sidebar-active': sidebarOpen }">

    <div class="navbar-wrapper">
        <div class="navbar-content">
            <!-- Sidebar Header -->
            <div class="flex items-center justify-center h-header-height px-6 border-b border-theme-sidebarbordercolor dark:border-themedark-sidebarbordercolor">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <img class="h-10 w-10" src="{{ asset('assets/images/logo.png') }}" alt="{{ config('app.name') }}" onerror="this.style.display='none'">
                        <div class="h-10 w-10 bg-primary rounded-lg flex items-center justify-center text-white font-bold text-base" style="display: none;">
                            {{ substr(config('app.name'), 0, 2) }}
                        </div>
                    </div>
                    <div class="ml-3">
                        <h2 class="text-lg font-semibold text-theme-headings dark:text-themedark-headings">{{ config('app.name') }}</h2>
                    </div>
                </div>
            </div>

            <!-- Sidebar Menu -->
            <nav class="pc-navbar">
                <!-- Dashboard -->
                <div class="pc-item">
                    <a href="{{ route('dashboard') }}"
                       class="pc-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                        </svg>
                        <span>Dashboard</span>
                    </a>
                </div>

                <!-- Users -->
                @can('view-users')
                <div class="pc-item">
                    <a href="{{ route('users.index') }}"
                       class="pc-link {{ request()->routeIs('users.*') ? 'active' : '' }}">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                        <span>Utilisateurs</span>
                    </a>
                </div>
                @endcan

                <!-- Taxes -->
                <div class="mt-6">
                    <div class="px-3 py-2">
                        <h3 class="text-xs font-semibold uppercase tracking-wider text-muted">Gestion des Taxes</h3>
                    </div>

                    <div class="pc-item">
                        <a href="#" class="pc-link">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                            <span>Déclarations</span>
                        </a>
                    </div>

                    <div class="pc-item">
                        <a href="#" class="pc-link">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>Paiements</span>
                        </a>
                    </div>

                    <div class="pc-item">
                        <a href="#" class="pc-link">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            <span>Rapports</span>
                        </a>
                    </div>
                </div>

                <!-- Administration -->
                @can('admin-access')
                <div class="mt-6">
                    <div class="px-3 py-2">
                        <h3 class="text-xs font-semibold uppercase tracking-wider text-muted">Administration</h3>
                    </div>

                    <div class="pc-item">
                        <a href="#" class="pc-link">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <span>Paramètres</span>
                        </a>
                    </div>

                    <div class="pc-item">
                        <a href="#" class="pc-link">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                            <span>Permissions</span>
                        </a>
                    </div>
                </div>
                @endcan
            </nav>

            <!-- Sidebar Footer -->
            <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-theme-sidebarbordercolor dark:border-themedark-sidebarbordercolor bg-theme-sidebaruserbg dark:bg-themedark-sidebaruserbg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name={{ urlencode(Auth::user()->name ?? 'User') }}&color=4680FF&background=EBF3FF" alt="{{ Auth::user()->name ?? 'User' }}">
                    </div>
                    <div class="ml-3 flex-1 min-w-0">
                        <p class="text-sm font-medium truncate text-theme-headings dark:text-themedark-headings">{{ Auth::user()->name ?? 'Utilisateur' }}</p>
                        <p class="text-xs truncate text-theme-sidebarcolor dark:text-themedark-sidebarcolor">{{ Auth::user()->email ?? '' }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</aside>
