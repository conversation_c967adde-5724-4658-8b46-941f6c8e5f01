<!-- Theme Customizer -->
<div class="pct-customizer">
    <div class="pct-c-btn">
        <button class="btn btn-light-dark" id="customizer-btn">
            <i class="ph-duotone ph-gear-six"></i>
        </button>
    </div>
    <div class="pct-c-content">
        <div class="pct-header">
            <h5>Personnalisation</h5>
            <button class="btn btn-sm btn-light-dark" id="customizer-close">
                <i class="ph-duotone ph-x"></i>
            </button>
        </div>
        <div class="pct-body">
            <!-- Theme Mode -->
            <div class="pct-section">
                <h6>Mode du thème</h6>
                <div class="row">
                    <div class="col-6">
                        <div class="d-grid">
                            <button class="preset-btn btn" data-value="light" onclick="layout_change('light')">
                                <svg class="pc-icon text-warning">
                                    <use xlink:href="#custom-sun-1"></use>
                                </svg>
                                <span>Clair</span>
                            </button>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-grid">
                            <button class="preset-btn btn" data-value="dark" onclick="layout_change('dark')">
                                <svg class="pc-icon text-primary">
                                    <use xlink:href="#custom-moon"></use>
                                </svg>
                                <span>Sombre</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Layout Type -->
            <div class="pct-section">
                <h6>Type de mise en page</h6>
                <div class="row">
                    <div class="col-6">
                        <div class="d-grid">
                            <button class="preset-btn btn active" data-value="vertical">
                                <span>Verticale</span>
                            </button>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-grid">
                            <button class="preset-btn btn" data-value="horizontal">
                                <span>Horizontale</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar Caption -->
            <div class="pct-section">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="sidebar-caption" checked>
                    <label class="form-check-label" for="sidebar-caption">Légendes de la sidebar</label>
                </div>
            </div>
            
            <!-- RTL -->
            <div class="pct-section">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="rtl-mode">
                    <label class="form-check-label" for="rtl-mode">Mode RTL</label>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Customizer toggle
    const customizerBtn = document.getElementById('customizer-btn');
    const customizerClose = document.getElementById('customizer-close');
    const customizer = document.querySelector('.pct-customizer');
    
    if (customizerBtn) {
        customizerBtn.addEventListener('click', function() {
            customizer.classList.add('active');
        });
    }
    
    if (customizerClose) {
        customizerClose.addEventListener('click', function() {
            customizer.classList.remove('active');
        });
    }
    
    // Preset buttons
    document.querySelectorAll('.preset-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const section = this.closest('.pct-section');
            section.querySelectorAll('.preset-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // Sidebar caption toggle
    const sidebarCaptionToggle = document.getElementById('sidebar-caption');
    if (sidebarCaptionToggle) {
        sidebarCaptionToggle.addEventListener('change', function() {
            const html = document.documentElement;
            html.setAttribute('data-pc-sidebar-caption', this.checked);
        });
    }
    
    // RTL toggle
    const rtlToggle = document.getElementById('rtl-mode');
    if (rtlToggle) {
        rtlToggle.addEventListener('change', function() {
            const html = document.documentElement;
            if (this.checked) {
                html.setAttribute('dir', 'rtl');
                html.setAttribute('data-pc-direction', 'rtl');
            } else {
                html.setAttribute('dir', 'ltr');
                html.setAttribute('data-pc-direction', 'ltr');
            }
        });
    }
});
</script>
