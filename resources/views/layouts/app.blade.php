<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" 
      class="preset-1" 
      data-pc-sidebar-caption="true" 
      data-pc-layout="vertical" 
      data-pc-direction="ltr" 
      dir="ltr" 
      data-pc-theme_contrast="" 
      data-pc-theme="light">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', 'Dashboard') | {{ config('app.name', 'Able Pro') }}</title>
    
    <!-- Meta Description -->
    <meta name="description" content="@yield('description', 'Able Pro Dashboard - Laravel Admin Template')">
    <meta name="keywords" content="Laravel admin template, Dashboard UI Kit, Dashboard Template, Backend Panel">
    <meta name="author" content="Phoenixcoded">
    
    <!-- Favicon -->
    <link rel="icon" href="{{ asset('assets/images/favicon.svg') }}" type="image/x-icon">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="{{ asset('assets/fonts/phosphor/duotone/style.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/tabler-icons.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/feather.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/fontawesome.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/material.css') }}">
    
    <!-- Page specific CSS -->
    @stack('styles')
    
    <!-- Main CSS -->
    @vite(['resources/css/app.css'])
    
    <!-- Additional head content -->
    @stack('head')
</head>

<body>
    <!-- Preloader -->
    <div class="loader-bg">
        <div class="loader-track">
            <div class="loader-fill"></div>
        </div>
    </div>

    <!-- Layout Wrapper -->
    <div class="layout-wrapper">
        
        <!-- Sidebar -->
        @include('layouts.partials.sidebar')
        
        <!-- Header -->
        @include('layouts.partials.header')
        
        <!-- Main Content -->
        <div class="pc-container">
            <div class="pc-content">
                
                <!-- Breadcrumb -->
                @if(isset($breadcrumbs) || View::hasSection('breadcrumb'))
                    @include('layouts.partials.breadcrumb')
                @endif
                
                <!-- Page Content -->
                <main>
                    @yield('content')
                    {{ $slot ?? '' }}
                </main>
                
            </div>
        </div>
        
        <!-- Footer -->
        @include('layouts.partials.footer')
        
    </div>

    <!-- Theme Customizer -->
    @include('layouts.partials.customizer')

    <!-- Scripts -->
    <script src="{{ asset('assets/js/plugins/simplebar.min.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/popper.min.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/feather.min.js') }}"></script>
    
    <!-- Main JS -->
    @vite(['resources/js/app.js'])
    
    <!-- Page specific scripts -->
    @stack('scripts')
    
    <!-- Additional body content -->
    @stack('body')

    <script>
        // Hide preloader
        window.addEventListener('load', function() {
            const preloader = document.querySelector('.loader-bg');
            if (preloader) {
                preloader.style.opacity = '0';
                setTimeout(() => {
                    preloader.style.display = 'none';
                }, 500);
            }
        });

        // Initialize theme
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Feather icons
            if (typeof feather !== 'undefined') {
                feather.replace();
            }

            // Initialize sidebar
            const sidebarToggle = document.getElementById('sidebar-hide');
            const mobileSidebarToggle = document.getElementById('mobile-collapse');
            const sidebar = document.querySelector('.pc-sidebar');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    sidebar.classList.toggle('pc-sidebar-hide');
                });
            }

            if (mobileSidebarToggle) {
                mobileSidebarToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    sidebar.classList.toggle('mob-sidebar-active');
                });
            }

            // Theme switcher
            window.layout_change = function(theme) {
                const html = document.documentElement;
                html.setAttribute('data-pc-theme', theme);
                localStorage.setItem('theme', theme);
            };

            // Load saved theme
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                document.documentElement.setAttribute('data-pc-theme', savedTheme);
            }
        });
    </script>
</body>
</html>
