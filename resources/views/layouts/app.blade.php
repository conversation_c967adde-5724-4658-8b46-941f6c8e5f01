<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <meta name="description" content="@yield('meta_description', 'Application de gestion des taxes')">
        <meta name="keywords" content="@yield('meta_keywords', 'taxes, gestion, administration')">

        <title>@yield('title', config('app.name', 'TaxeApp')) - {{ config('app.name', 'TaxeApp') }}</title>

        <!-- Favicon -->
        <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <!-- Icons -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/heroicons@2.0.18/24/outline/index.css">

        <!-- Styles -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
        @stack('styles')
    </head>
    <body class="h-full font-sans antialiased" x-data="{ sidebarOpen: false }" style="background-color: var(--theme-bodybg); color: var(--theme-bodycolor);">
        <div class="main-layout">
            <!-- Sidebar -->
            @include('layouts.partials.sidebar')

            <!-- Mobile sidebar overlay -->
            <div x-show="sidebarOpen"
                 x-transition:enter="transition-opacity ease-linear duration-300"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="transition-opacity ease-linear duration-300"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 class="fixed inset-0 z-30 bg-gray-600 bg-opacity-75 lg:hidden"
                 @click="sidebarOpen = false">
            </div>

            <!-- Header -->
            @include('layouts.partials.header')

            <!-- Main Content -->
            <main class="main-content">
                <div class="content-wrapper">
                    <!-- Page Header -->
                    @if (isset($header) || isset($pageTitle))
                        <div class="mb-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h1 class="text-2xl font-semibold" style="color: var(--theme-headings)">
                                        {{ $pageTitle ?? $header ?? 'Dashboard' }}
                                    </h1>
                                    @if(isset($pageDescription))
                                        <p class="mt-1 text-sm" style="color: var(--theme-secondarytextcolor)">{{ $pageDescription }}</p>
                                    @endif
                                </div>
                                @if(isset($headerActions))
                                    <div class="flex space-x-3">
                                        {{ $headerActions }}
                                    </div>
                                @endif
                            </div>

                            <!-- Breadcrumb -->
                            @if(isset($breadcrumb))
                                <nav class="mt-4" aria-label="Breadcrumb">
                                    <ol class="flex items-center space-x-2 text-sm" style="color: var(--theme-secondarytextcolor)">
                                        {{ $breadcrumb }}
                                    </ol>
                                </nav>
                            @endif
                        </div>
                    @endif

                    <!-- Flash Messages -->
                    @if(session('success'))
                        <div class="mb-6 rounded-lg bg-success-50 p-4 border border-success-200">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-success-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-success-800">{{ session('success') }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="mb-6 rounded-lg bg-danger-50 p-4 border border-danger-200">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-danger-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-danger-800">{{ session('error') }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(session('warning'))
                        <div class="mb-6 rounded-lg bg-warning-50 p-4 border border-warning-200">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-warning-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-warning-800">{{ session('warning') }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Page Content -->
                    {{ $slot }}
                </div>
            </main>

            <!-- Footer -->
            @include('layouts.partials.footer')
        </div>

        <!-- Scripts -->
        @include('layouts.partials.scripts')
        @stack('scripts')
    </body>
</html>
