@props([
    'label' => null,
    'name' => null,
    'type' => 'text',
    'placeholder' => null,
    'required' => false,
    'disabled' => false,
    'readonly' => false,
    'error' => null,
    'help' => null,
    'icon' => null,
    'iconPosition' => 'left',
    'size' => 'md'
])

@php
    $inputId = $name ?? 'input_' . uniqid();
    $hasError = $error || $errors->has($name);
    
    $baseClasses = 'block w-full border rounded-lg shadow-sm focus:outline-none focus:ring-1 transition-colors duration-200';
    
    $sizeClasses = [
        'sm' => 'px-3 py-1.5 text-sm',
        'md' => 'px-3 py-2 text-sm',
        'lg' => 'px-4 py-3 text-base'
    ];
    
    $stateClasses = $hasError 
        ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500' 
        : 'border-gray-300 focus:border-primary-500 focus:ring-primary-500';
    
    $disabledClasses = $disabled ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : 'bg-white';
    
    $iconPadding = '';
    if ($icon) {
        $iconPadding = $iconPosition === 'left' ? 'pl-10' : 'pr-10';
    }
    
    $classes = $baseClasses . ' ' . ($sizeClasses[$size] ?? $sizeClasses['md']) . ' ' . $stateClasses . ' ' . $disabledClasses . ' ' . $iconPadding;
@endphp

<div {{ $attributes->only('class') }}>
    @if($label)
        <label for="{{ $inputId }}" class="form-label">
            {{ $label }}
            @if($required)
                <span class="text-danger-500">*</span>
            @endif
        </label>
    @endif
    
    <div class="relative">
        @if($icon)
            <div class="absolute inset-y-0 {{ $iconPosition === 'left' ? 'left-0 pl-3' : 'right-0 pr-3' }} flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    {!! $icon !!}
                </svg>
            </div>
        @endif
        
        <input 
            type="{{ $type }}"
            id="{{ $inputId }}"
            name="{{ $name }}"
            placeholder="{{ $placeholder }}"
            {{ $required ? 'required' : '' }}
            {{ $disabled ? 'disabled' : '' }}
            {{ $readonly ? 'readonly' : '' }}
            value="{{ old($name, $attributes->get('value', '')) }}"
            {{ $attributes->except(['class', 'value'])->merge(['class' => $classes]) }}
        >
    </div>
    
    @if($help && !$hasError)
        <p class="mt-1 text-sm text-gray-600">{{ $help }}</p>
    @endif
    
    @if($hasError)
        <p class="form-error">
            {{ $error ?? $errors->first($name) }}
        </p>
    @endif
</div>
