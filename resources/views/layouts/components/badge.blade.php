@props([
    'variant' => 'primary',
    'size' => 'md',
    'dot' => false,
    'removable' => false
])

@php
    $baseClasses = 'inline-flex items-center font-medium rounded-full';
    
    $variantClasses = [
        'primary' => 'bg-primary-100 text-primary-800',
        'secondary' => 'bg-secondary-100 text-secondary-800',
        'success' => 'bg-success-100 text-success-800',
        'warning' => 'bg-warning-100 text-warning-800',
        'danger' => 'bg-danger-100 text-danger-800',
        'info' => 'bg-info-100 text-info-800',
        'gray' => 'bg-gray-100 text-gray-800'
    ];
    
    $sizeClasses = [
        'sm' => 'px-2 py-0.5 text-xs',
        'md' => 'px-2.5 py-0.5 text-xs',
        'lg' => 'px-3 py-1 text-sm'
    ];
    
    $dotColors = [
        'primary' => 'bg-primary-400',
        'secondary' => 'bg-secondary-400',
        'success' => 'bg-success-400',
        'warning' => 'bg-warning-400',
        'danger' => 'bg-danger-400',
        'info' => 'bg-info-400',
        'gray' => 'bg-gray-400'
    ];
    
    $classes = $baseClasses . ' ' . ($variantClasses[$variant] ?? $variantClasses['primary']) . ' ' . ($sizeClasses[$size] ?? $sizeClasses['md']);
@endphp

<span {{ $attributes->merge(['class' => $classes]) }}>
    @if($dot)
        <span class="w-1.5 h-1.5 rounded-full mr-1.5 {{ $dotColors[$variant] ?? $dotColors['primary'] }}"></span>
    @endif
    
    {{ $slot }}
    
    @if($removable)
        <button type="button" class="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-black hover:bg-opacity-10 focus:outline-none focus:bg-black focus:bg-opacity-10" onclick="this.closest('span').remove()">
            <span class="sr-only">Supprimer</span>
            <svg class="w-2 h-2" fill="currentColor" viewBox="0 0 8 8">
                <path d="M1.41 0L0 1.41 2.59 4 0 6.59 1.41 8 4 5.41 6.59 8 8 6.59 5.41 4 8 1.41 6.59 0 4 2.59 1.41 0z"/>
            </svg>
        </button>
    @endif
</span>
