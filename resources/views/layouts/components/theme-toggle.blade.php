@props([
    'size' => 'md'
])

@php
    $sizeClasses = [
        'sm' => 'h-4 w-4',
        'md' => 'h-5 w-5',
        'lg' => 'h-6 w-6'
    ];
    
    $iconSize = $sizeClasses[$size] ?? $sizeClasses['md'];
@endphp

<div x-data="themeToggle()" class="relative">
    <button @click="toggleTheme()" 
            class="p-2 rounded-lg transition-colors duration-200 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary"
            :title="isDark ? 'Passer en mode clair' : 'Passer en mode sombre'"
            style="color: var(--theme-headercolor);">
        
        <!-- Icône soleil (mode clair) -->
        <svg x-show="!isDark" 
             class="{{ $iconSize }}" 
             fill="none" 
             stroke="currentColor" 
             viewBox="0 0 24 24">
            <path stroke-linecap="round" 
                  stroke-linejoin="round" 
                  stroke-width="2" 
                  d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z">
            </path>
        </svg>
        
        <!-- Icône lune (mode sombre) -->
        <svg x-show="isDark" 
             class="{{ $iconSize }}" 
             fill="none" 
             stroke="currentColor" 
             viewBox="0 0 24 24">
            <path stroke-linecap="round" 
                  stroke-linejoin="round" 
                  stroke-width="2" 
                  d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z">
            </path>
        </svg>
    </button>
</div>

<script>
    function themeToggle() {
        return {
            isDark: false,
            
            init() {
                // Vérifier le thème sauvegardé ou la préférence système
                const savedTheme = localStorage.getItem('theme');
                const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                
                this.isDark = savedTheme === 'dark' || (!savedTheme && systemPrefersDark);
                this.applyTheme();
                
                // Écouter les changements de préférence système
                window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                    if (!localStorage.getItem('theme')) {
                        this.isDark = e.matches;
                        this.applyTheme();
                    }
                });
            },
            
            toggleTheme() {
                this.isDark = !this.isDark;
                this.applyTheme();
                this.saveTheme();
            },
            
            applyTheme() {
                if (this.isDark) {
                    document.documentElement.setAttribute('data-pc-theme', 'dark');
                    document.documentElement.classList.add('dark');
                } else {
                    document.documentElement.removeAttribute('data-pc-theme');
                    document.documentElement.classList.remove('dark');
                }
            },
            
            saveTheme() {
                localStorage.setItem('theme', this.isDark ? 'dark' : 'light');
            }
        }
    }
</script>
