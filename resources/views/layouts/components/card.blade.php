@props([
    'title' => null,
    'subtitle' => null,
    'headerActions' => null,
    'footer' => null,
    'padding' => true,
    'shadow' => 'soft',
    'border' => true
])

@php
    $classes = 'bg-white rounded-lg';
    
    if ($border) {
        $classes .= ' border border-gray-200';
    }
    
    $shadowClasses = [
        'none' => '',
        'soft' => 'shadow-soft',
        'medium' => 'shadow-medium',
        'large' => 'shadow-large'
    ];
    
    $classes .= ' ' . ($shadowClasses[$shadow] ?? 'shadow-soft');
@endphp

<div {{ $attributes->merge(['class' => $classes]) }}>
    @if($title || $subtitle || $headerActions)
        <div class="card-header">
            <div class="flex items-center justify-between">
                <div>
                    @if($title)
                        <h3 class="text-lg font-medium text-gray-900">{{ $title }}</h3>
                    @endif
                    @if($subtitle)
                        <p class="mt-1 text-sm text-gray-600">{{ $subtitle }}</p>
                    @endif
                </div>
                @if($headerActions)
                    <div class="flex items-center space-x-2">
                        {{ $headerActions }}
                    </div>
                @endif
            </div>
        </div>
    @endif

    <div class="{{ $padding ? 'card-body' : '' }}">
        {{ $slot }}
    </div>

    @if($footer)
        <div class="card-footer">
            {{ $footer }}
        </div>
    @endif
</div>
