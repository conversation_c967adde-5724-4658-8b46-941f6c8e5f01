<x-app-layout>
    <x-slot name="pageTitle">Exemple de Page</x-slot>
    <x-slot name="pageDescription">Démonstration des composants Able Pro</x-slot>
    
    <x-slot name="breadcrumb">
        <li>
            <a href="{{ route('dashboard') }}" class="text-gray-500 hover:text-gray-700">Dashboard</a>
        </li>
        <li class="flex items-center">
            <svg class="h-4 w-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 111.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span class="ml-2 text-gray-900 font-medium">Exemple</span>
        </li>
    </x-slot>
    
    <x-slot name="headerActions">
        <x-layouts.components.button variant="primary">
            Nouvelle Action
        </x-layouts.components.button>
    </x-slot>

    <div class="space-y-6">
        <!-- Section Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <x-layouts.components.card title="Statistiques" subtitle="Données en temps réel">
                <div class="text-center">
                    <div class="text-3xl font-bold text-primary">1,234</div>
                    <div class="text-sm" style="color: var(--theme-secondarytextcolor)">Utilisateurs actifs</div>
                </div>
            </x-layouts.components.card>

            <x-layouts.components.card title="Revenus" subtitle="Ce mois">
                <div class="text-center">
                    <div class="text-3xl font-bold text-success">€45,678</div>
                    <div class="text-sm" style="color: var(--theme-secondarytextcolor)">+12% vs mois dernier</div>
                </div>
            </x-layouts.components.card>

            <x-layouts.components.card title="Commandes" subtitle="Aujourd'hui">
                <div class="text-center">
                    <div class="text-3xl font-bold text-warning">89</div>
                    <div class="text-sm" style="color: var(--theme-secondarytextcolor)">En cours de traitement</div>
                </div>
            </x-layouts.components.card>
        </div>

        <!-- Section Buttons -->
        <x-layouts.components.card title="Exemples de Boutons">
            <div class="space-y-4">
                <div class="flex flex-wrap gap-3">
                    <x-layouts.components.button variant="primary">Primary</x-layouts.components.button>
                    <x-layouts.components.button variant="secondary">Secondary</x-layouts.components.button>
                    <x-layouts.components.button variant="success">Success</x-layouts.components.button>
                    <x-layouts.components.button variant="warning">Warning</x-layouts.components.button>
                    <x-layouts.components.button variant="danger">Danger</x-layouts.components.button>
                    <x-layouts.components.button variant="outline">Outline</x-layouts.components.button>
                    <x-layouts.components.button variant="ghost">Ghost</x-layouts.components.button>
                </div>
                
                <div class="flex flex-wrap gap-3">
                    <x-layouts.components.button variant="primary" size="sm">Small</x-layouts.components.button>
                    <x-layouts.components.button variant="primary" size="md">Medium</x-layouts.components.button>
                    <x-layouts.components.button variant="primary" size="lg">Large</x-layouts.components.button>
                </div>
                
                <div class="flex flex-wrap gap-3">
                    <x-layouts.components.button variant="primary" :icon="'<path stroke-linecap=\'round\' stroke-linejoin=\'round\' stroke-width=\'2\' d=\'M12 6v6m0 0v6m0-6h6m-6 0H6\'></path>'">
                        Avec Icône
                    </x-layouts.components.button>
                    <x-layouts.components.button variant="primary" :loading="true">
                        Loading
                    </x-layouts.components.button>
                    <x-layouts.components.button variant="primary" :disabled="true">
                        Disabled
                    </x-layouts.components.button>
                </div>
            </div>
        </x-layouts.components.card>

        <!-- Section Badges -->
        <x-layouts.components.card title="Exemples de Badges">
            <div class="space-y-4">
                <div class="flex flex-wrap gap-3">
                    <x-layouts.components.badge variant="primary">Primary</x-layouts.components.badge>
                    <x-layouts.components.badge variant="secondary">Secondary</x-layouts.components.badge>
                    <x-layouts.components.badge variant="success">Success</x-layouts.components.badge>
                    <x-layouts.components.badge variant="warning">Warning</x-layouts.components.badge>
                    <x-layouts.components.badge variant="danger">Danger</x-layouts.components.badge>
                    <x-layouts.components.badge variant="info">Info</x-layouts.components.badge>
                </div>
                
                <div class="flex flex-wrap gap-3">
                    <x-layouts.components.badge variant="primary" :dot="true">Avec Point</x-layouts.components.badge>
                    <x-layouts.components.badge variant="success" :removable="true">Supprimable</x-layouts.components.badge>
                    <x-layouts.components.badge variant="warning" size="lg">Large</x-layouts.components.badge>
                </div>
            </div>
        </x-layouts.components.card>

        <!-- Section Formulaire -->
        <x-layouts.components.card title="Exemple de Formulaire">
            <form class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <x-layouts.components.input 
                        name="first_name" 
                        label="Prénom" 
                        placeholder="Entrez votre prénom"
                        :required="true"
                    />
                    
                    <x-layouts.components.input 
                        name="last_name" 
                        label="Nom" 
                        placeholder="Entrez votre nom"
                        :required="true"
                    />
                </div>
                
                <x-layouts.components.input 
                    name="email" 
                    type="email"
                    label="Email" 
                    placeholder="<EMAIL>"
                    :icon="'<path stroke-linecap=\'round\' stroke-linejoin=\'round\' stroke-width=\'2\' d=\'M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207\'></path>'"
                    help="Nous ne partagerons jamais votre email"
                    :required="true"
                />
                
                <x-layouts.components.input 
                    name="phone" 
                    label="Téléphone" 
                    placeholder="+33 1 23 45 67 89"
                    :icon="'<path stroke-linecap=\'round\' stroke-linejoin=\'round\' stroke-width=\'2\' d=\'M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\'></path>'"
                />
                
                <div class="flex justify-end space-x-3">
                    <x-layouts.components.button variant="outline" type="button">
                        Annuler
                    </x-layouts.components.button>
                    <x-layouts.components.button variant="primary" type="submit">
                        Enregistrer
                    </x-layouts.components.button>
                </div>
            </form>
        </x-layouts.components.card>

        <!-- Section Tableau -->
        <x-layouts.components.card title="Exemple de Tableau">
            <div class="overflow-x-auto">
                <table class="table">
                    <thead class="table-header">
                        <tr>
                            <th class="table-header-cell">Nom</th>
                            <th class="table-header-cell">Email</th>
                            <th class="table-header-cell">Statut</th>
                            <th class="table-header-cell">Date</th>
                            <th class="table-header-cell">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="table-body">
                        <tr>
                            <td class="table-cell font-medium">John Doe</td>
                            <td class="table-cell"><EMAIL></td>
                            <td class="table-cell">
                                <x-layouts.components.badge variant="success">Actif</x-layouts.components.badge>
                            </td>
                            <td class="table-cell">01/01/2024</td>
                            <td class="table-cell">
                                <div class="flex space-x-2">
                                    <x-layouts.components.button variant="ghost" size="sm">Voir</x-layouts.components.button>
                                    <x-layouts.components.button variant="ghost" size="sm">Modifier</x-layouts.components.button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="table-cell font-medium">Jane Smith</td>
                            <td class="table-cell"><EMAIL></td>
                            <td class="table-cell">
                                <x-layouts.components.badge variant="warning">En attente</x-layouts.components.badge>
                            </td>
                            <td class="table-cell">02/01/2024</td>
                            <td class="table-cell">
                                <div class="flex space-x-2">
                                    <x-layouts.components.button variant="ghost" size="sm">Voir</x-layouts.components.button>
                                    <x-layouts.components.button variant="ghost" size="sm">Modifier</x-layouts.components.button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </x-layouts.components.card>
    </div>

    @push('scripts')
        <script>
            // Exemple d'utilisation des fonctionnalités JavaScript
            document.addEventListener('DOMContentLoaded', function() {
                // Test des notifications
                setTimeout(() => {
                    Utils.showToast('Page chargée avec succès !', 'success');
                }, 1000);
            });
        </script>
    @endpush
</x-app-layout>
