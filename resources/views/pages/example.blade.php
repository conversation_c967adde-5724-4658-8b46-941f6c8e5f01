@extends('layouts.app')

@section('title', 'Exemple de Page')
@section('description', 'Démonstration des composants Able Pro')

@section('breadcrumb')
    <li class="breadcrumb-item">
        <a href="#">Composants</a>
    </li>
    <li class="breadcrumb-item active" aria-current="page">Exemple</li>
@endsection

@section('content')

    <div class="row">
        <!-- Section Cards -->
        <div class="col-xl-4 col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Statistiques</h5>
                    <small class="text-muted">Données en temps réel</small>
                </div>
                <div class="card-body text-center">
                    <div class="h2 text-primary mb-2">1,234</div>
                    <p class="text-muted mb-0">Utilisateurs actifs</p>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Revenus</h5>
                    <small class="text-muted">Ce mois</small>
                </div>
                <div class="card-body text-center">
                    <div class="h2 text-success mb-2">€45,678</div>
                    <p class="text-muted mb-0">+12% vs mois dernier</p>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Commandes</h5>
                    <small class="text-muted">Aujourd'hui</small>
                </div>
                <div class="card-body text-center">
                    <div class="h2 text-warning mb-2">89</div>
                    <p class="text-muted mb-0">En cours de traitement</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Section Buttons -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Exemples de Boutons</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <button class="btn btn-primary me-2">Primary</button>
                        <button class="btn btn-secondary me-2">Secondary</button>
                        <button class="btn btn-success me-2">Success</button>
                        <button class="btn btn-warning me-2">Warning</button>
                        <button class="btn btn-danger me-2">Danger</button>
                        <button class="btn btn-outline-primary me-2">Outline</button>
                        <button class="btn btn-light me-2">Light</button>
                    </div>

                    <div class="mb-3">
                        <button class="btn btn-primary btn-sm me-2">Small</button>
                        <button class="btn btn-primary me-2">Medium</button>
                        <button class="btn btn-primary btn-lg me-2">Large</button>
                    </div>

                    <div class="mb-0">
                        <button class="btn btn-primary me-2">
                            <i class="ph-duotone ph-plus me-1"></i>
                            Avec Icône
                        </button>
                        <button class="btn btn-primary me-2" disabled>
                            <span class="spinner-border spinner-border-sm me-1" role="status"></span>
                            Loading
                        </button>
                        <button class="btn btn-primary me-2" disabled>
                            Disabled
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Section Badges -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Exemples de Badges</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <span class="badge bg-primary me-2">Primary</span>
                        <span class="badge bg-secondary me-2">Secondary</span>
                        <span class="badge bg-success me-2">Success</span>
                        <span class="badge bg-warning me-2">Warning</span>
                        <span class="badge bg-danger me-2">Danger</span>
                        <span class="badge bg-info me-2">Info</span>
                    </div>

                    <div class="mb-0">
                        <span class="badge bg-primary me-2">
                            <i class="ph-duotone ph-dot-outline me-1"></i>
                            Avec Point
                        </span>
                        <span class="badge bg-success me-2">
                            Supprimable
                            <i class="ph-duotone ph-x ms-1"></i>
                        </span>
                        <span class="badge bg-warning fs-6 me-2">Large</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Section Formulaire -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Exemple de Formulaire</h5>
                </div>
                <div class="card-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label" for="first_name">Prénom <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" placeholder="Entrez votre prénom" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label" for="last_name">Nom <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" placeholder="Entrez votre nom" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label" for="email">Email <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="ph-duotone ph-envelope"></i>
                                </span>
                                <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                            </div>
                            <div class="form-text">Nous ne partagerons jamais votre email</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label" for="phone">Téléphone</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="ph-duotone ph-phone"></i>
                                </span>
                                <input type="tel" class="form-control" id="phone" name="phone" placeholder="+33 1 23 45 67 89">
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-outline-secondary">Annuler</button>
                            <button type="submit" class="btn btn-primary">Enregistrer</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Section Tableau -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Exemple de Tableau</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Nom</th>
                                    <th>Email</th>
                                    <th>Statut</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-medium">John Doe</td>
                                    <td><EMAIL></td>
                                    <td>
                                        <span class="badge bg-success">Actif</span>
                                    </td>
                                    <td>01/01/2024</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary me-1">Voir</button>
                                        <button class="btn btn-sm btn-outline-secondary">Modifier</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Jane Smith</td>
                                    <td><EMAIL></td>
                                    <td>
                                        <span class="badge bg-warning">En attente</span>
                                    </td>
                                    <td>02/01/2024</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary me-1">Voir</button>
                                        <button class="btn btn-sm btn-outline-secondary">Modifier</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

@push('scripts')
    <script>
        // Exemple d'utilisation des fonctionnalités JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Test des notifications
            console.log('Page Able Pro chargée avec succès !');

            // Initialiser les tooltips si Bootstrap est disponible
            if (typeof bootstrap !== 'undefined') {
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            }
        });
    </script>
@endpush
@endsection
