const plugin = require('tailwindcss/plugin');

module.exports = plugin(function ({ addComponents, theme }) {
  addComponents({
    '.alert': {
      '@apply px-5 py-3 mb-3 rounded-lg border': {},
      '.alert-link': {
        '@apply font-bold': {}
      },
      '.alert-heading': {
        '@apply text-inherit': {}
      },
      hr: {
        '@apply border-inherit': {}
      },
      '&.alert-dismissible': {
        '@apply relative pr-10 rtl:pl-12 rtl:pr-5': {},
        '[data-pc-dismiss="alert"]': {
          '@apply absolute ltr:right-2 top-2 rtl:left-2': {}
        }
      }
    },
    '.alert-primary': {
      '@apply border-primary-500/20 bg-primary-500/20 text-primary-800': {}
    },
    '.alert-secondary': {
      '@apply border-secondary-500/20 bg-secondary-500/20 text-secondary-800 dark:text-secondary-200': {}
    },
    '.alert-success': {
      '@apply border-success-500/10 bg-success-500/10 text-success-800': {}
    },
    '.alert-danger': {
      '@apply border-danger-500/20 bg-danger-500/20 text-danger-800': {}
    },
    '.alert-warning': {
      '@apply border-warning-500/20 bg-warning-500/20 text-warning-800': {}
    },
    '.alert-info': {
      '@apply border-info-500/20 bg-info-500/20 text-info-800': {}
    },
    '.alert-dark': {
      '@apply border-dark-500/20 bg-dark-500/20 text-dark-800 dark:text-dark-200': {}
    }
  });
});
