import './bootstrap';

import Alpine from 'alpinejs';

// Configuration Alpine.js
window.Alpine = Alpine;

// Store global pour l'état de l'application
Alpine.store('app', {
    sidebarOpen: false,
    loading: false,
    notifications: [],

    // Méthodes pour la sidebar
    toggleSidebar() {
        this.sidebarOpen = !this.sidebarOpen;
    },

    closeSidebar() {
        this.sidebarOpen = false;
    },

    // Méthodes pour le loading
    setLoading(state) {
        this.loading = state;
    },

    // Méthodes pour les notifications
    addNotification(notification) {
        const id = Date.now();
        this.notifications.push({ id, ...notification });

        // Auto-remove après 5 secondes
        setTimeout(() => {
            this.removeNotification(id);
        }, 5000);

        return id;
    },

    removeNotification(id) {
        this.notifications = this.notifications.filter(n => n.id !== id);
    }
});

// Composants Alpine.js personnalisés
Alpine.data('dropdown', () => ({
    open: false,

    toggle() {
        this.open = !this.open;
    },

    close() {
        this.open = false;
    }
}));

Alpine.data('modal', () => ({
    open: false,

    show() {
        this.open = true;
        document.body.style.overflow = 'hidden';
    },

    hide() {
        this.open = false;
        document.body.style.overflow = '';
    }
}));

Alpine.data('tabs', (defaultTab = 0) => ({
    activeTab: defaultTab,

    setActiveTab(index) {
        this.activeTab = index;
    },

    isActive(index) {
        return this.activeTab === index;
    }
}));

Alpine.data('accordion', () => ({
    openItems: [],

    toggle(index) {
        if (this.openItems.includes(index)) {
            this.openItems = this.openItems.filter(i => i !== index);
        } else {
            this.openItems.push(index);
        }
    },

    isOpen(index) {
        return this.openItems.includes(index);
    }
}));

// Directives Alpine.js personnalisées
Alpine.directive('tooltip', (el, { expression }, { evaluate }) => {
    const text = evaluate(expression);

    el.addEventListener('mouseenter', () => {
        const tooltip = document.createElement('div');
        tooltip.className = 'absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg pointer-events-none';
        tooltip.textContent = text;
        document.body.appendChild(tooltip);

        const rect = el.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';

        el._tooltip = tooltip;
    });

    el.addEventListener('mouseleave', () => {
        if (el._tooltip) {
            el._tooltip.remove();
            el._tooltip = null;
        }
    });
});

// Démarrer Alpine.js
Alpine.start();

// Fonctions globales utilitaires
window.showToast = function(message, type = 'success') {
    Alpine.store('app').addNotification({
        message,
        type
    });
};

window.confirmAction = function(message, callback) {
    if (confirm(message)) {
        callback();
    }
};

// Gestion des erreurs globales
window.addEventListener('error', function(e) {
    console.error('Erreur JavaScript:', e.error);
    // En production, envoyer à un service de monitoring
});

// Gestion des erreurs de promesses non catchées
window.addEventListener('unhandledrejection', function(e) {
    console.error('Promesse rejetée:', e.reason);
    // En production, envoyer à un service de monitoring
});

// Auto-hide des messages flash
document.addEventListener('DOMContentLoaded', function() {
    const flashMessages = document.querySelectorAll('.flash-message');
    flashMessages.forEach(message => {
        setTimeout(() => {
            message.style.transition = 'opacity 0.5s ease-out';
            message.style.opacity = '0';
            setTimeout(() => {
                if (message.parentNode) {
                    message.parentNode.removeChild(message);
                }
            }, 500);
        }, 5000);
    });
});
