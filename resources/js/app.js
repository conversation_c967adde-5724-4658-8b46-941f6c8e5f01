import './bootstrap';

import Alpine from 'alpinejs';

// Configuration Alpine.js
window.Alpine = Alpine;

// Store global pour l'état de l'application
Alpine.store('app', {
    sidebarOpen: false,
    loading: false,
    notifications: [],

    // Méthodes pour la sidebar
    toggleSidebar() {
        this.sidebarOpen = !this.sidebarOpen;
    },

    closeSidebar() {
        this.sidebarOpen = false;
    },

    // Méthodes pour le loading
    setLoading(state) {
        this.loading = state;
    },

    // Méthodes pour les notifications
    addNotification(notification) {
        const id = Date.now();
        this.notifications.push({ id, ...notification });

        // Auto-remove après 5 secondes
        setTimeout(() => {
            this.removeNotification(id);
        }, 5000);

        return id;
    },

    removeNotification(id) {
        this.notifications = this.notifications.filter(n => n.id !== id);
    }
});

// Composants Alpine.js personnalisés
Alpine.data('dropdown', () => ({
    open: false,

    toggle() {
        this.open = !this.open;
    },

    close() {
        this.open = false;
    }
}));

Alpine.data('modal', () => ({
    open: false,

    show() {
        this.open = true;
        document.body.style.overflow = 'hidden';
    },

    hide() {
        this.open = false;
        document.body.style.overflow = '';
    }
}));

Alpine.data('tabs', (defaultTab = 0) => ({
    activeTab: defaultTab,

    setActiveTab(index) {
        this.activeTab = index;
    },

    isActive(index) {
        return this.activeTab === index;
    }
}));

Alpine.data('accordion', () => ({
    openItems: [],

    toggle(index) {
        if (this.openItems.includes(index)) {
            this.openItems = this.openItems.filter(i => i !== index);
        } else {
            this.openItems.push(index);
        }
    },

    isOpen(index) {
        return this.openItems.includes(index);
    }
}));

// Directives Alpine.js personnalisées
Alpine.directive('tooltip', (el, { expression }, { evaluate }) => {
    const text = evaluate(expression);

    el.addEventListener('mouseenter', () => {
        const tooltip = document.createElement('div');
        tooltip.className = 'absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg pointer-events-none';
        tooltip.textContent = text;
        document.body.appendChild(tooltip);

        const rect = el.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';

        el._tooltip = tooltip;
    });

    el.addEventListener('mouseleave', () => {
        if (el._tooltip) {
            el._tooltip.remove();
            el._tooltip = null;
        }
    });
});

// Démarrer Alpine.js
Alpine.start();

// Fonctions globales utilitaires
window.showToast = function(message, type = 'success') {
    Alpine.store('app').addNotification({
        message,
        type
    });
};

window.confirmAction = function(message, callback) {
    if (confirm(message)) {
        callback();
    }
};

// Gestion des erreurs globales
window.addEventListener('error', function(e) {
    console.error('Erreur JavaScript:', e.error);
    // En production, envoyer à un service de monitoring
});

// Gestion des erreurs de promesses non catchées
window.addEventListener('unhandledrejection', function(e) {
    console.error('Promesse rejetée:', e.reason);
    // En production, envoyer à un service de monitoring
});

// Auto-hide des messages flash
document.addEventListener('DOMContentLoaded', function() {
    const flashMessages = document.querySelectorAll('.flash-message');
    flashMessages.forEach(message => {
        setTimeout(() => {
            message.style.transition = 'opacity 0.5s ease-out';
            message.style.opacity = '0';
            setTimeout(() => {
                if (message.parentNode) {
                    message.parentNode.removeChild(message);
                }
            }, 500);
        }, 5000);
    });
});

// Able Pro Core Functions
class AblePro {
    constructor() {
        this.init();
    }

    init() {
        this.initializeComponents();
        this.setupEventListeners();
        this.loadTheme();
    }

    initializeComponents() {
        // Initialize dropdowns
        this.initDropdowns();
        
        // Initialize tooltips
        this.initTooltips();
        
        // Initialize sidebar
        this.initSidebar();
        
        // Initialize theme switcher
        this.initThemeSwitcher();
    }

    initDropdowns() {
        document.addEventListener('click', (e) => {
            const dropdownToggle = e.target.closest('[data-pc-toggle="dropdown"]');
            
            if (dropdownToggle) {
                e.preventDefault();
                const dropdown = dropdownToggle.nextElementSibling;
                
                // Close other dropdowns
                document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                    if (menu !== dropdown) {
                        menu.classList.remove('show');
                    }
                });
                
                // Toggle current dropdown
                if (dropdown && dropdown.classList.contains('dropdown-menu')) {
                    dropdown.classList.toggle('show');
                }
            } else {
                // Close all dropdowns when clicking outside
                document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        });
    }

    initTooltips() {
        // Simple tooltip implementation
        document.querySelectorAll('[data-pc-toggle="tooltip"]').forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                const title = e.target.getAttribute('data-pc-title');
                if (title) {
                    this.showTooltip(e.target, title);
                }
            });
            
            element.addEventListener('mouseleave', (e) => {
                this.hideTooltip();
            });
        });
    }

    showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'pc-tooltip';
        tooltip.textContent = text;
        tooltip.style.cssText = `
            position: absolute;
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 9999;
            pointer-events: none;
            white-space: nowrap;
        `;
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
        
        this.currentTooltip = tooltip;
    }

    hideTooltip() {
        if (this.currentTooltip) {
            this.currentTooltip.remove();
            this.currentTooltip = null;
        }
    }

    initSidebar() {
        const sidebar = document.querySelector('.pc-sidebar');
        const sidebarToggle = document.getElementById('sidebar-hide');
        const mobileSidebarToggle = document.getElementById('mobile-collapse');
        
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', (e) => {
                e.preventDefault();
                sidebar?.classList.toggle('pc-sidebar-hide');
                localStorage.setItem('sidebar-collapsed', sidebar?.classList.contains('pc-sidebar-hide'));
            });
        }
        
        if (mobileSidebarToggle) {
            mobileSidebarToggle.addEventListener('click', (e) => {
                e.preventDefault();
                sidebar?.classList.toggle('mob-sidebar-active');
            });
        }
        
        // Load saved sidebar state
        const sidebarCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
        if (sidebarCollapsed) {
            sidebar?.classList.add('pc-sidebar-hide');
        }
        
        // Close mobile sidebar when clicking outside
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 1024) {
                if (!e.target.closest('.pc-sidebar') && !e.target.closest('#mobile-collapse')) {
                    sidebar?.classList.remove('mob-sidebar-active');
                }
            }
        });
    }

    initThemeSwitcher() {
        // Theme switching functionality
        window.layout_change = (theme) => {
            document.documentElement.setAttribute('data-pc-theme', theme);
            localStorage.setItem('theme', theme);
            
            // Update theme icon
            this.updateThemeIcon(theme);
            
            // Dispatch theme change event
            window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
        };
    }

    updateThemeIcon(theme) {
        const themeIcon = document.getElementById('theme-icon');
        if (themeIcon) {
            const iconMap = {
                'light': 'ti-sun',
                'dark': 'ti-moon',
                'default': 'ti-device-desktop'
            };
            
            themeIcon.className = `ti ${iconMap[theme] || 'ti-sun'}`;
        }
    }

    loadTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-pc-theme', savedTheme);
        this.updateThemeIcon(savedTheme);
    }

    setupEventListeners() {
        // Handle offcanvas
        document.addEventListener('click', (e) => {
            const trigger = e.target.closest('[data-pc-toggle="offcanvas"]');
            if (trigger) {
                e.preventDefault();
                const target = trigger.getAttribute('data-pc-target');
                const offcanvas = document.querySelector(target);
                if (offcanvas) {
                    offcanvas.classList.add('show');
                    document.body.style.overflow = 'hidden';
                }
            }
            
            const dismiss = e.target.closest('[data-pc-dismiss]');
            if (dismiss) {
                e.preventDefault();
                const target = dismiss.getAttribute('data-pc-dismiss');
                const offcanvas = document.querySelector(target);
                if (offcanvas) {
                    offcanvas.classList.remove('show');
                    document.body.style.overflow = '';
                }
            }
        });
        
        // Handle modals
        document.addEventListener('click', (e) => {
            const trigger = e.target.closest('[data-pc-toggle="modal"]');
            if (trigger) {
                e.preventDefault();
                const target = trigger.getAttribute('data-pc-target');
                const modal = document.querySelector(target);
                if (modal) {
                    modal.classList.add('show');
                    modal.style.display = 'block';
                    document.body.classList.add('modal-open');
                }
            }
            
            const modalDismiss = e.target.closest('[data-pc-dismiss="modal"]');
            if (modalDismiss) {
                e.preventDefault();
                const modal = modalDismiss.closest('.modal');
                if (modal) {
                    modal.classList.remove('show');
                    modal.style.display = 'none';
                    document.body.classList.remove('modal-open');
                }
            }
        });
        
        // Close modal when clicking backdrop
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.classList.remove('show');
                e.target.style.display = 'none';
                document.body.classList.remove('modal-open');
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // ESC to close modals/offcanvas
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                const openOffcanvas = document.querySelector('.offcanvas.show');
                
                if (openModal) {
                    openModal.classList.remove('show');
                    openModal.style.display = 'none';
                    document.body.classList.remove('modal-open');
                }
                
                if (openOffcanvas) {
                    openOffcanvas.classList.remove('show');
                    document.body.style.overflow = '';
                }
            }
            
            // Ctrl+K for search
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('.form-search input');
                searchInput?.focus();
            }
        });
    }
}

// Utility functions
window.AbleProUtils = {
    // Show notification
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, duration);
    },
    
    // Confirm dialog
    confirm(message, callback) {
        if (window.confirm(message)) {
            callback();
        }
    },
    
    // Loading state
    setLoading(element, loading = true) {
        if (loading) {
            element.disabled = true;
            element.innerHTML = '<i class="ti ti-loader animate-spin mr-2"></i> Chargement...';
        } else {
            element.disabled = false;
            element.innerHTML = element.getAttribute('data-original-text') || 'Valider';
        }
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new AblePro();
    
    // Remove preloader
    const loader = document.querySelector('.loader-bg');
    if (loader) {
        setTimeout(() => {
            loader.style.opacity = '0';
            setTimeout(() => loader.remove(), 300);
        }, 500);
    }
});

// Export for use in other modules
export default AblePro;
